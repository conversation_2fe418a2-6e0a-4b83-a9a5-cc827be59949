const FinalJSToTSConverter = require('./final_converter');
const fs = require('fs');

// 测试 ADController.js 的转换
const converter = new FinalJSToTSConverter();

// 读取文件内容
const jsContent = fs.readFileSync('./scripts/ADController.js', 'utf8');

// 调试：检查各个解析步骤
console.log('🔍 开始调试转换过程...\n');

// 1. 检查导入
const imports = converter.extractImports(jsContent);
console.log('1. 导入解析结果:', imports);

// 2. 检查类信息
const classInfo = converter.extractClassInfo(jsContent);
console.log('2. 类信息解析结果:', classInfo);

// 2.1 手动检查继承关系
const extendsMatch = jsContent.match(/\}\((\$\d*\$?\d*\w+)\.(\w+)\);/);
console.log('2.1 继承关系匹配结果:', extendsMatch);

// 2.2 检查文件末尾
const lastLines = jsContent.split('\n').slice(-5);
console.log('2.2 文件末尾5行:', lastLines);

// 2.3 尝试更宽松的匹配
const extendsMatch2 = jsContent.match(/\}\(\$2MVC\.MVC\.MController\);/);
console.log('2.3 直接匹配结果:', extendsMatch2);

// 3. 检查装饰器
const decorators = converter.extractDecorators(jsContent);
console.log('3. 装饰器解析结果:', decorators);

// 4. 检查枚举
const enums = converter.extractEnums(jsContent);
console.log('4. 枚举解析结果:', enums);

// 转换
const tsContent = converter.convertJSToTS(jsContent);

if (tsContent) {
    // 写入文件
    fs.writeFileSync('./ADController_test.ts', tsContent, 'utf8');
    console.log('\n✅ 转换成功！');

    // 显示前30行
    const lines = tsContent.split('\n');
    console.log('\n前30行预览:');
    console.log(lines.slice(0, 30).join('\n'));
} else {
    console.log('\n❌ 转换失败 - 可能是类信息解析失败');
}
