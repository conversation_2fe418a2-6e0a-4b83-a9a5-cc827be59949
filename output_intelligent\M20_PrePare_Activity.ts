import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2ModeChainsModel from "./ModeChainsModel";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_PrePare_Activity extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
