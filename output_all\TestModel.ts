import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pool from "./Pool";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2FColliderManager from "./FColliderManager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2AlertManager from "./AlertManager";
import $2GoodsUIItem from "./GoodsUIItem";
import $2RBadgeModel from "./RBadgeModel";
import $2Game from "./Game";
import $2CompManager from "./CompManager";
import $2PropertyVo from "./PropertyVo";
import $2NodePool from "./NodePool";
import $2SettingModel from "./SettingModel";
import $2TaskModel from "./TaskModel";

const { ccclass, property, menu } = cc._decorator;

export default class TestModel extends $2MVC.MVC.BaseModel {
    // TODO: 添加属性和方法
}