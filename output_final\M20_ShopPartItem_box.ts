import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2UIManager from "./UIManager";
import $2M20_PartItem from "./M20_PartItem";
import $2M20_ShopPartItem from "./M20_ShopPartItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_box extends $2M20_ShopPartItem.default {
    get boxLv() {
        return this.mode.fightinfopack.getVal("boxlv") || 1;
    }

    onLoad() {
        this.bar = this.node.getChildByName("middle").getChildByName("boxbar");
            this.bar.active = true;
            this.bar.getChildByName("btninfo").on(cc.Node.EventType.TOUCH_START, this.showBoxInfo, this);
            $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.M20_UpdateBoxExp, this.checkUpgrade, this);
            this.mode.fightinfopack.has("boxlv") || this.mode.fightinfopack.addGoods("boxlv");
    }

    showBoxInfo() {
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBoxInfo", $2MVC.MVC.openArgs().setIsNeedLoading(false));
    }

    onDestroy() {
        this.bar.getChildByName("btninfo").off(cc.Node.EventType.TOUCH_START, this.showBoxInfo, this);
    }

    checkUpgrade() {
        const e = this.mode.fightinfopack.getVal("boxcost");
            const t = $2Cfg.Cfg.BoxLevelExp.get(this.boxLv).BoxlvExp;
            if (this.boxLv <= $2Cfg.Cfg.BoxLevelExp.getArray().length && e >= t) {
              this.mode.fightinfopack.setVal("boxcost", e - t);
              this.mode.fightinfopack.addGoods("boxlv");
              const o = $2MVC.MVC.openArgs();
              o.setIsNeedLoading(false);
              o.setParam({
                content: {
                  from: this.boxLv
                }
              });
              this.boxLv <= $2Cfg.Cfg.BoxLevelExp.getArray().length && $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_ShopBoxInfo", o);
            }
            this.refreshData();
    }

    getList() {
        const e = this;
            return [...$2Cfg.Cfg.BagShopItem.getArray(].filter(function (t) {
              return t.id == 1e4 + e.boxLv || t.id == 10100 + e.boxLv;
            }));
    }

    resetView() {
        const t = this;
            super.resetView();
            this.node.getChildByName("middle").active = true;
            this.bar || (this.bar = this.node.getChildByName("middle").getChildByName("boxbar"));
            // this.contentnode.getComponent(cc.Layout).paddingLeft = 38;
            const o = false;
            const i = 0;
            if (this.boxLv > $2Cfg.Cfg.BoxLevelExp.getArray().length) {
              o = true;
            } else {
              i = $2Cfg.Cfg.BoxLevelExp.get(this.boxLv).BoxlvExp;
            }
            const n = this.mode.fightinfopack.getVal("boxcost");
            cc.tween(this.bar.getChildByName("progress").getComponent(cc.ProgressBar)).to(.5, {
              progress: o ? 1 : n / i
            }).call(function () {
              t.bar.getChildByName("lv").getComponent(cc.Label).string = "LV." + (o ? $2Cfg.Cfg.BoxLevelExp.getArray().length : t.mode.fightinfopack.getVal("boxlv"));
            }).start();
            this.bar.getChildByName("progress").getComponentInChildren(cc.Label).string = o ? "已满级" : n + "/" + i;
            for (const r = 0; r < this.content.length; r++) {
              const a = this.content[r];
              const s = this.contentnode.children[r] || cc.instantiate(this.cloneitem);
              s.setAttribute({
                parent: this.contentnode
              });
              s.getComponent($2M20_PartItem.default).setdata(a, true, 1);
            }
    }

}
