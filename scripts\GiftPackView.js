var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2ShareButton = require("ShareButton");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2AlertManager = require("AlertManager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_GiftPackView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.adddestopN = null;
    t.adddestopRp = null;
    t.navRp = null;
    t.infonode = [];
    t.onGet = function () { };
    t.onCloseFun = function () { };
    t._curcheck = 1;
    t.packFun = {
      TT_ShareGiftView: function () {
        t.labelArr[0].string = "分享领奖(" + t.userVo.dailyData.TT_ShareGiftView + ")";
        t.labelArr[0].node.parent.setGrayAllChild(t.userVo.dailyData.TT_ShareGiftView <= 0);
        t.userVo.dailyData.TT_ShareGiftView <= 0 && t.labelArr[0].node.parent.getComponent($2ShareButton.default).destroy();
        t.onGet = function () {
          var e = [{
            id: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin,
            type: $2GameSeting.GameSeting.GoodsType.Money,
            num: 200
          }, {
            id: $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond,
            type: $2GameSeting.GameSeting.GoodsType.Money,
            num: 100
          }];
          $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, e);
          t.close();
        };
      },
      PlatformTTSidebarRewardView: function () {
        cc.log("PlatformTTSidebarRewardView");
        var e = !$2Manager.Manager.vo.userVo.dailyData.ttNavReward && "021036" == $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene);
        var o = "021036" == $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene) && $2Manager.Manager.vo.userVo.dailyData.ttNavReward;
        var i = $2Manager.Manager.vo.knapsackVo.has("TTDestopReward") && $2Manager.Manager.vo.knapsackVo.has("TTScenc_destop") > 0;
        var n = !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward") && $2Manager.Manager.vo.knapsackVo.has("TTScenc_destop") > 0;
        t.labelArr[0].node.parent.getComponent(cc.Button).interactable = true;
        t.labelArr[1].node.parent.getComponent(cc.Button).interactable = true;
        if (1 == t._curcheck) {
          t.labelArr[0].string = (o && (t.labelArr[0].node.parent.getComponent(cc.Button).interactable = false), o ? "今日已领取" : e ? "领取奖励" : "前往");
        } else {
          t.labelArr[1].string = (i && (t.labelArr[1].node.parent.getComponent(cc.Button).interactable = false), i ? "已领取" : n ? "领取奖励" : "添加桌面");
        }

        t.infonode.forEach(function (e) {
          return e.active = false;
        });
        t.infonode[t._curcheck - 1].active = true;
        t.adddestopRp.active = n;
        t.navRp.active = e;
        t.onGet = function () {
          if (1 == t._curcheck) {
            if (e) {
              $2Manager.Manager.vo.userVo.dailyData.ttNavReward = true;
              $2Manager.Manager.vo.saveUserData();
              $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, 100);
              $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, 10);
              $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得钻石%d", 100));
              $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得体力%d", 10));
              $2Notifier.Notifier.send($2ListenID.ListenID.Main_ResetView);
              t.close();
            } else {
              $2Notifier.Notifier.send($2ListenID.ListenID.Platform_NavigateTo);
              t.close();
            }
          } else if (n) {
            $2Manager.Manager.vo.knapsackVo.addGoods("TTDestopReward", 1, $2GameSeting.GameSeting.GoodsType.System);
            $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, 100);
            $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, 500);
            $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得金币%d", 500));
            $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得钻石%d", 100));
            $2Notifier.Notifier.send($2ListenID.ListenID.Main_ResetView);
            t.close();
          } else {
            $2Notifier.Notifier.send($2ListenID.ListenID.Platform_AddtoDestop);
            t.close();
          }
        };
      }
    };
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setInfo = function () {
    var e;
    this.param && (this._curcheck = this.param.curCheck);
    null === (e = this.packFun[this.node.name]) || undefined === e || e.call();
  };
  Object.defineProperty(_ctor.prototype, "userVo", {
    get: function () {
      return $2Manager.Manager.vo.userVo;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changleToggle = function (e, t) {
    var o;
    this._curcheck = Number(t);
    null === (o = this.packFun[this.node.name]) || undefined === o || o.call();
  };
  _ctor.prototype.onBtn = function (e, t) {
    cc.log("name", t);
    switch (t) {
      case "Get":
        this.onGet();
    }
  };
  _ctor.prototype.onClose = function () {
    this.onCloseFun();
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "adddestopN", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "adddestopRp", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "navRp", undefined);
  cc__decorate([ccp_property([cc.Node])], _ctor.prototype, "infonode", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Main/GiftPackView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)], _ctor);
}($2Pop.Pop);
exports.default = def_GiftPackView;