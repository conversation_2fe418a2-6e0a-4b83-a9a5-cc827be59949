var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SettingView = undefined;
var $2CallID = require("CallID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Pop = require("Pop");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2Commonguide = require("Commonguide");
var $2SettingModel = require("SettingModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var exp_SettingView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.btnAudio = null;
    t.btnMusic = null;
    t.btnDub = null;
    t.btnShake = null;
    t.btnSavingMode = null;
    t.btnSwitch = null;
    t.setting3 = null;
    t.audioPro = null;
    t.musicPro = null;
    t.audioNode = null;
    t.musicNode = null;
    t.rectMusic = null;
    t.rectAudio = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setInfo = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Commonguide.default.isinitGuide && $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_GuideChange, this.guideChange, this);
    this.musicNode.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchMusicProEnd, this);
    this.musicNode.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchMusicProEnd, this);
    this.audioNode.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchAudioProEnd, this);
    this.audioNode.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchAudioProEnd, this);
  };
  _ctor.prototype.guideChange = function () {
    this.nodeArr[0].active = true;
    $2Notifier.Notifier.changeListener(false, $2ListenID.ListenID.Fight_GuideChange, this.guideChange, this);
  };
  _ctor.prototype.onTouchAudioProEnd = function (e) {
    this.changeAudioValue(this.getPrecentValue(e, 1));
  };
  _ctor.prototype.getPrecentValue = function (e, t) {
    undefined === t && (t = 0);
    var o = this.musicPro.node;
    1 == t && (o = this.audioPro.node);
    var i = (o.parent.convertToNodeSpaceAR(e.getLocation()).x - 0) / 218;
    i < 0 && (i = 0);
    i > 1 && (i = 1);
    return i;
  };
  _ctor.prototype.onTouchMusicProEnd = function (e) {
    this.changeMusicValue(this.getPrecentValue(e));
  };
  _ctor.prototype.onOpen = function () {
    this._setMusicValue($2SettingModel.default.instance.toggle.musicValue);
    this._setAudioValue($2SettingModel.default.instance.toggle.audioValue);
    this._setShakeEnable($2SettingModel.default.instance.toggle.shake);
    var e = $2Game.Game.getCutMode();
    cc.find("bg/layout/btnhome", this.node).setActive([$2Game.Game.Mode.BACKPACKHERO, $2Game.Game.Mode.CHAINS, $2Game.Game.Mode.NONE].includes(e));
    cc.find("bg/layout/IOSRecoveryBuy", this.node).setActive(wonderSdk.isIOS && !$2Manager.Manager.vo.knapsackVo.has("BuyRestoreBtn"));
    cc.find("bg/layout/Replay", this.node).setActive([$2Game.Game.Mode.CHAINS].includes(e));
    this.node.getComByPath(cc.Label, "bg/layout/btnhome/txt").string = e == $2Game.Game.Mode.NONE ? "兑换码" : "退出";
    this.labelArr[0].string = "-" + $2Manager.Manager.vo.switchVo.fightStamina;
  };
  _ctor.prototype._setMusicEnable = function (e) {
    this.btnMusic.children[1].active = e;
    this.btnMusic.children[2].x = e ? 40 : -40;
  };
  _ctor.prototype._setMusicValue = function (e) {
    var t = 297 * e;
    this.musicPro.progress = e;
    this.rectMusic.node.setPosition(t, 0);
  };
  _ctor.prototype._setAudioEnable = function (e) {
    this.btnAudio.children[1].active = e;
    this.btnAudio.children[2].active = e;
  };
  _ctor.prototype._setAudioValue = function (e) {
    this.audioPro.progress = e;
    var t = 297 * e;
    this.rectAudio.node.setPosition(t, 0);
  };
  _ctor.prototype._setDubEnable = function (e) {
    this.btnDub.children[0].active = !e;
    this.btnDub.children[1].active = e;
  };
  _ctor.prototype._setShakeEnable = function (e) {
    this.btnShake.children[0].active = !e;
    this.btnShake.children[1].active = e;
  };
  _ctor.prototype._setSavingMode = function (e) {
    this.btnSavingMode.children[0].active = !e;
    this.btnSavingMode.children[1].active = e;
  };
  _ctor.prototype.onBtn = function (e, t) {
    var o = this;
    switch (t) {
      case "Replay":
        $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
          type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
          val: $2Manager.Manager.vo.switchVo.fightStamina,
          call: function (e) {
            if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Game_Replay);
              o.close();
            }
          }
        });
        break;
      case "IOSRecoveryBuy":
        $2Notifier.Notifier.send($2ListenID.ListenID.Shop_RecoveryBuy);
        $2AlertManager.AlertManager.showNormalTips("恢复购买成功");
        this.close();
    }
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  _ctor.prototype.onClickAudio = function () {};
  _ctor.prototype.onClickMusic = function () {};
  _ctor.prototype.changeMusicValue = function (e) {
    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_ValueMusic, e);
    this._setMusicValue(e);
  };
  _ctor.prototype.changeAudioValue = function (e) {
    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_ValueAudio, e);
    this._setAudioValue(e);
  };
  _ctor.prototype.onClickDub = function () {
    var e = !$2SettingModel.default.instance.toggle.dub;
    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_EnableDub, e);
    this._setDubEnable(e);
  };
  _ctor.prototype.onClickShake = function () {
    var e = !$2SettingModel.default.instance.toggle.shake;
    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_EnableShake, e);
    this._setShakeEnable(e);
    e || wonderSdk.vibrate(0);
  };
  _ctor.prototype.onBackHomeClick = function () {
    var e = this;
    this.close();
    var t = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
    if (0 == t) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Activity_OpenExchangeCode, false);
    } else if (t == $2Game.Game.Mode.CATGAME) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_Win);
    } else if (t == $2Game.Game.Mode.BACKPACKHERO) {
      $2AlertManager.AlertManager.showSelectAlert({
        title: "是否退出",
        desc: "退出战斗不会获得任何奖励，确定退出吗？",
        confirmText: "退出",
        confirm: function () {
          e.exitGame();
        },
        tag: $2GameSeting.GameSeting.TweenType.Game
      });
    } else {
      this.exitGame();
    }
  };
  _ctor.prototype.exitGame = function () {
    var e;
    $2UIManager.UIManager.cleanQueue();
    $2Manager.Manager.ui.cleanDelayView();
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ClickBackHome);
    null === (e = $2Game.Game.mgr) || undefined === e || e.sendEvent("returnHome");
    $2Notifier.Notifier.send($2ListenID.ListenID.ResetView);
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain, 1);
  };
  _ctor.prototype.onClickSaving = function () {
    var e = !$2SettingModel.default.instance.toggle.power;
    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_SetSavingMode, e);
    this._setSavingMode(e);
  };
  _ctor.prototype.onClickSwitchAccount = function () {
    wonderSdk.logout();
  };
  _ctor.prototype.onClickPrivacy = function () {
    wonderSdk.isNative || $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenUserAndPolicy, "隐私政策", "");
  };
  _ctor.prototype.onClickUserProtocol = function () {
    wonderSdk.isNative || $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenUserAndPolicy, "用户协议", "");
  };
  _ctor.prototype.onClickClearData = function () {
    $2AlertManager.AlertManager.showAlert($2AlertManager.AlertType.SELECT, {
      desc: "清除缓存，此操作将退出游戏并需自行重启，请确认是否继续操作",
      confirmText: "确认",
      cancelText: "取消",
      confirm: function () {
        $2Manager.Manager.storage.clear();
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Clear_Cache", {
          ClearCacheCount: 1
        });
        cc.game.emit("exit_game");
      }
    });
  };
  _ctor.prototype.onClickExchangeCode = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Activity_OpenExchangeCode, false);
  };
  _ctor.prototype.onClickWeChatGameClub = function () {};
  _ctor.prototype.onClickGameService = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenGameService);
  };
  _ctor.prototype.onClickServer = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Login_OpenSelectServerView, false);
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnAudio", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnMusic", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnDub", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnShake", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnSavingMode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnSwitch", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "setting3", undefined);
  cc__decorate([ccp_property(cc.ProgressBar)], _ctor.prototype, "audioPro", undefined);
  cc__decorate([ccp_property(cc.ProgressBar)], _ctor.prototype, "musicPro", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "audioNode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "musicNode", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "rectMusic", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "rectAudio", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Setting/SettingView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Panel), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.SettingView = exp_SettingView;