import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2Manager from "./Manager";
import $2FCollider from "./FCollider";
import $2Game from "./Game";
import $2BaseEntity from "./BaseEntity";
import $2OrganismBase from "./OrganismBase";
import $2PropertyVo from "./PropertyVo";
import $2BulletVoPool from "./BulletVoPool";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class BulletBase extends $2OrganismBase.default {
    // TODO: 添加属性和方法
}