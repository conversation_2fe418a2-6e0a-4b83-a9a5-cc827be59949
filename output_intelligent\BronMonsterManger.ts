import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2Notifier from "./Notifier";
import $2GameUtil from "./GameUtil";
import $2NodePool from "./NodePool";
import $2Game from "./Game";
import $2Monster from "./Monster";
import $2SkillModel from "./SkillModel";
import $2RewardEvent from "./RewardEvent";

const { ccclass, property, menu } = cc._decorator;

export default class BronMonsterManger extends cc.Comonnt {
    // TODO: 添加属性和方法
}
