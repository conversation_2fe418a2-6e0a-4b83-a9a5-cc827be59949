import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2Notifier from "./Notifier";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2PropertyVo from "./PropertyVo";
import $2Buff from "./Buff";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export class Buff_Default extends $2Buff.Buff.BuffItem {
}

@ccclass
export class Buff_Excute extends $2Buff.Buff.BuffItem {
    excuteTime: any;
    _excuteDt: any;

    constructor() {
        super();
        this.excuteTime = 1;
        this._excuteDt = 0;
    }

    onLoad() {
        // TODO: 实现方法体
    }

    onUpdate(t) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnTime extends Buff_Excute {
    onLoad() {
        // TODO: 实现方法体
    }

    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_Effect extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    unload() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnSpawnHurt extends $2Buff.Buff.BuffItem {
    changeListener(e) {
        // TODO: 实现方法体
    }

    onLoad() {
        // TODO: 实现方法体
    }

    onSpawnHurt() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnVampirism extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    changeListener(e) {
        // TODO: 实现方法体
    }

    onVampirism() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_CurrencyReward extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    addLayer() {
        // TODO: 实现方法体
    }

    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnBehit extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    changeListener(e) {
        // TODO: 实现方法体
    }

    onBeHit(e) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnKill extends $2Buff.Buff.BuffItem {
    changeListener(e) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_HPLink extends $2Buff.Buff.BuffItem {
    changeListener(e) {
        // TODO: 实现方法体
    }

    onBeHit() {
        // TODO: 实现方法体
    }

    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_ContinuousRecovery extends Buff_Excute {
    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_HPLinkOnce extends Buff_HPLink {
    changeListener(t) {
        // TODO: 实现方法体
    }

    onFight_RoundState() {
        // TODO: 实现方法体
    }

    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_EntityDead extends $2Buff.Buff.BuffItem {
    changeListener(e) {
        // TODO: 实现方法体
    }

    onDead() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_VicinityHurt extends Buff_Excute {
    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_Halo extends Buff_Excute {
    onLoad() {
        // TODO: 实现方法体
    }

    unload() {
        // TODO: 实现方法体
    }

    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_Hurt extends Buff_Excute {
    onLoad() {
        // TODO: 实现方法体
    }

    excute() {
        // TODO: 实现方法体
    }

    unload() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnUseSkillHurt extends $2Buff.Buff.BuffItem {
    changeListener(e) {
        // TODO: 实现方法体
    }

    onEntityUseSkill() {
        // TODO: 实现方法体
    }

    onLoad() {
        // TODO: 实现方法体
    }

    unload() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_SubSkill extends $2Buff.Buff.BuffItem {
}

@ccclass
export class Buff_AtkFocus extends Buff_OnSpawnHurt {
    onLoad() {
        // TODO: 实现方法体
    }

    onSpawnHurt(e, t) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_AdrenalTechnology extends Buff_OnSpawnHurt {
    onLoad() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnSkillUseUnload extends $2Buff.Buff.BuffItem {
    changeListener(e) {
        // TODO: 实现方法体
    }

    onEntityUseSkill() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_ReboundDam extends Buff_OnBehit {
    onLoad() {
        // TODO: 实现方法体
    }

    onBeHit(e) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnKillLayout extends Buff_OnKill {
    onLoad() {
        // TODO: 实现方法体
    }

    onKill() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_HitBack extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    onUpdate(t) {
        // TODO: 实现方法体
    }

    unload() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnLifeVal extends Buff_OnBehit {
    onBeHit() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnSpawnHurtAddArmor extends Buff_OnSpawnHurt {
    onSpawnHurt() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnBehitAddArmor extends Buff_OnBehit {
    onBeHit() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_RestoreArmor extends Buff_Excute {
    excute() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnSkill extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    changeListener(e) {
        // TODO: 实现方法体
    }

    onSkill() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_Vampire extends Buff_OnSpawnHurt {
    onSpawnHurt(e, t) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_AssociationProp extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_ResistDamage extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

    unuseLayer(t) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_SetSlash extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_OnRoundState extends $2Buff.Buff.BuffItem {
    changeListener(t) {
        // TODO: 实现方法体
    }

}

@ccclass
export class Buff_ReplaceRole extends $2Buff.Buff.BuffItem {
    onLoad() {
        // TODO: 实现方法体
    }

}
