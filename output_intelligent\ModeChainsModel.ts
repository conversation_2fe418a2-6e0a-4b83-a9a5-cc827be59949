import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2MVC from "./MVC";
import $2GameSeting from "./GameSeting";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2LevelMgr from "./LevelMgr";
import $2RecordVo from "./RecordVo";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2MChains from "./MChains";

const { ccclass, property, menu } = cc._decorator;

export default class ModeChainsModel extends $2MVC.MVC.BaseModel {
    // TODO: 添加属性和方法
}
