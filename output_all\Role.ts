import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2SoundCfg from "./SoundCfg";
import $2Notifier from "./Notifier";
import $2StateMachine from "./StateMachine";
import $2Manager from "./Manager";
import $2Game from "./Game";
import $2Buff from "./Buff";
import $2SkillManager from "./SkillManager";
import $2SkillModel from "./SkillModel";
import $2RoleState from "./RoleState";
import $2PropertyVo from "./PropertyVo";
import $2BaseEntity from "./BaseEntity";
import $2OrganismBase from "./OrganismBase";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class Role extends $2OrganismBase.default {
    // TODO: 添加属性和方法
}