import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem extends cc.Component {
    @property(cc.Node)
    contentnode: cc.Node = null;

    @property(cc.Label)
    title: cc.Label = null;

    constructor() {
        super();
        this.content = [];
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    setData(e: any) {
        const t = this;
            this.data = e;
            this.cloneitem || $2Manager.Manager.loader.loadPrefab(e.prefabe).then(function (e) {
              t.cloneitem = e;
              t.refreshData();
            });
    }

    resetView() {
        this.title.string = this.data.title;
    }

    refreshData() {
        this.content = this.getList();
            this.resetView();
    }

    getList() {
        return [];
    }

    onEnable() {
        this.changeListener(true);
    }

    onDisable() {
        this.changeListener(false);
    }

    changeListener(e: any) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetView, this);
    }

}
