import $2BaseSdk from "./BaseSdk";
import $2AudioAdapter from "./AudioAdapter";
import $2GameUtil from "./GameUtil";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2ttPostbackCtl from "./ttPostbackCtl";
import $2CallID from "./CallID";
import $2Manager from "./Manager";
import $2Api from "./Api";
import $2UIManager from "./UIManager";
import $2MVC from "./MVC";

const { ccclass, property, menu } = cc._decorator;

export default class ByteDance extends $2BaseSdk.BaseSdk {
    // TODO: 添加属性和方法
}
