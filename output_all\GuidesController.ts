import $2ListenID from "./ListenID";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2KnapsackVo from "./KnapsackVo";
import $2GuidesModel from "./GuidesModel";

const { ccclass, property, menu } = cc._decorator;

export default class GuidesController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}