import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2FCollider from "./FCollider";
import $2Monster from "./Monster";
import $2ModeChainsModel from "./ModeChainsModel";
import $2Buff from "./Buff";
import $2Cfg from "./Cfg";
import $2GameUtil from "./GameUtil";
import $2FCircleCollider from "./FCircleCollider";
import $2Game from "./Game";
import $2GameSeting from "./GameSeting";
import $2Intersection from "./Intersection";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class DragonBody extends $2Monster.Monster {
    // TODO: 添加属性和方法
}