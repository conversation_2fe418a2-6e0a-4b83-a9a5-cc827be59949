import $2CallID from "./CallID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Pop from "./Pop";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2Commonguide from "./Commonguide";
import $2SettingModel from "./SettingModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class SettingView extends $2Pop.Pop {
    // TODO: 添加属性和方法
}