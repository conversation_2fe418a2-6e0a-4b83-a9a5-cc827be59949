import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2Monster from "./Monster";
import $2Game from "./Game";
import $2SkillManager from "./SkillManager";
import $2ModeBulletsReboundModel from "./ModeBulletsReboundModel";
import $2MBRebound from "./MBRebound";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MBRMonster extends $2Monster.Monster {
    // TODO: 添加属性和方法
}