Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2LocalStorage = require("LocalStorage");
var def_ReportQueue = new (function () {
  function _ctor() {
    this._list = [];
    this._storageKey = "sdk_report_list";
    var e = $2LocalStorage.default.getItem(this._storageKey);
    this._list = e ? JSON.parse(e) : [];
  }
  _ctor.prototype.push = function (e) {
    var t = this._list.push(e);
    this.refeshStorage();
    return t;
  };
  _ctor.prototype.pop = function () {
    var e = this._list.shift();
    this.refeshStorage();
    return e;
  };
  _ctor.prototype.range = function (e, t) {
    var o = this._list.splice(e, t);
    this.refeshStorage();
    return o;
  };
  _ctor.prototype.refeshStorage = function () {
    var e = JSON.stringify(this._list);
    return $2LocalStorage.default.setItem(this._storageKey, e);
  };
  _ctor.prototype.len = function () {
    return this._list.length;
  };
  _ctor.prototype.getList = function () {
    return this._list;
  };
  return _ctor;
}())();
exports.default = def_ReportQueue;