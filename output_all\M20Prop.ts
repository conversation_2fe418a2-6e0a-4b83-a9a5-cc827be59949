import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2OrganismBase from "./OrganismBase";
import $2Game from "./Game";
import $2MBackpackHero from "./MBackpackHero";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Prop_Equip from "./M20Prop_Equip";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20Prop extends $2OrganismBase.default {
    // TODO: 添加属性和方法
}