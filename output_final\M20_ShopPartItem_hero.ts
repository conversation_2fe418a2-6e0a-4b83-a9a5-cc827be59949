import $2Cfg from "./Cfg";
import $2GameUtil from "./GameUtil";
import $2M20_ShopPartItem from "./M20_ShopPartItem";
import $2M20_Shop_HeroItem from "./M20_Shop_HeroItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_hero extends $2M20_ShopPartItem.default {
    resetView() {
        super.resetView();
            for (const t = 0; t < this.content.length; t++) {
              const o = this.content[t];
              const i = this.contentnode.children[t] || cc.instantiate(this.cloneitem);
              i.setAttribute({
                parent: this.contentnode
              });
              i.getComponent($2M20_Shop_HeroItem.default).setdata(o);
            }
    }

    getList() {
        return [...$2GameUtil.GameUtil.getRandomInArray($2Cfg.Cfg.BagShopItem.filter({
              type: 999
            }], 1));
    }

}
