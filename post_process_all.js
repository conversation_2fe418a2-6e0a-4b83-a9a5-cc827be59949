const fs = require('fs');
const path = require('path');

class PostProcessor {
    constructor() {
        this.processedCount = 0;
        this.fixedCount = 0;
    }

    // 批量后处理目录
    processDirectory(dirPath) {
        if (!fs.existsSync(dirPath)) {
            console.error(`❌ 目录不存在: ${dirPath}`);
            return;
        }

        const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.ts'));
        
        console.log(`🔧 开始后处理 ${files.length} 个文件...\n`);

        files.forEach(file => {
            const filePath = path.join(dirPath, file);
            this.processFile(filePath);
        });

        console.log(`\n✅ 后处理完成!`);
        console.log(`📊 处理文件: ${this.processedCount}`);
        console.log(`🔧 修正文件: ${this.fixedCount}`);
    }

    // 处理单个文件
    processFile(filePath) {
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;
        
        // 应用所有修正
        content = this.applyAllFixes(content);
        
        // 如果有变化，写回文件
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`🔧 修正: ${path.basename(filePath)}`);
            this.fixedCount++;
        }
        
        this.processedCount++;
    }

    // 应用所有修正
    applyAllFixes(content) {
        let fixed = content;

        // 1. 修正变量声明残留
        fixed = this.fixVariableDeclarations(fixed);
        
        // 2. 修正父类调用
        fixed = this.fixSuperCalls(fixed);
        
        // 3. 修正类名错误
        fixed = this.fixClassNames(fixed);
        
        // 4. 修正变量引用
        fixed = this.fixVariableReferences(fixed);
        
        // 5. 清理格式
        fixed = this.cleanupFormatting(fixed);
        
        return fixed;
    }

    // 修正变量声明
    fixVariableDeclarations(content) {
        return content
            // 移除简单的 this 别名声明
            .replace(/^\s*var [etoin] = this;\s*$/gm, '')
            
            // 移除未使用的变量声明
            .replace(/^\s*var [etoin];\s*$/gm, '')
            
            // 修正变量声明后的引用
            .replace(/var (e|t|o|i|n) = this;\s*\n\s*\1\./g, 'this.');
    }

    // 修正父类调用
    fixSuperCalls(content) {
        return content
            // 修正各种父类调用模式
            .replace(/(\w+)\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$2($3)')
            .replace(/this\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)')
            .replace(/e\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)')
            .replace(/t\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)')
            
            // 清理参数中的多余逗号
            .replace(/super\.(\w+)\(,\s*/g, 'super.$1(')
            .replace(/super\.(\w+)\(\s*,/g, 'super.$1(');
    }

    // 修正类名错误
    fixClassNames(content) {
        const classNameFixes = {
            'BuffEcut': 'Buff_Excute',
            'BuffExcut': 'Buff_Excute',
            'BuffOnTime': 'Buff_OnTime',
            'BuffHPLink': 'Buff_HPLink',
            'BuffOnBehit': 'Buff_OnBehit',
            'BuffOnKill': 'Buff_OnKill',
            'BuffOnSpawnHurt': 'Buff_OnSpawnHurt',
            'BuffOnVampirism': 'Buff_OnVampirism',
            'BuffCurrencyReward': 'Buff_CurrencyReward',
            'BuffEntityDead': 'Buff_EntityDead',
            'BuffVicinityHurt': 'Buff_VicinityHurt',
            'BuffHalo': 'Buff_Halo',
            'BuffHurt': 'Buff_Hurt'
        };

        let fixed = content;
        Object.entries(classNameFixes).forEach(([wrong, correct]) => {
            const regex = new RegExp(`\\b${wrong}\\b`, 'g');
            fixed = fixed.replace(regex, correct);
        });

        return fixed;
    }

    // 修正变量引用
    fixVariableReferences(content) {
        return content
            // 修正错误的 this 引用
            .replace(/this\.this\./g, 'this.')
            
            // 修正常见的变量引用错误
            .replace(/\be\.(\w+)/g, (match, prop) => {
                // 如果是已知的属性，替换为 this
                if (['owner', 'listener', 'cutVo', 'isWeight', 'mgr'].includes(prop)) {
                    return `this.${prop}`;
                }
                return match;
            })
            
            // 修正 null 检查
            .replace(/null === \((\w+) = this\.(\w+)\) \|\| \w+ === undefined \|\| \w+\./g, 
                    'null === (this.$2) || this.$2 === undefined || this.$2.');
    }

    // 清理格式
    cleanupFormatting(content) {
        return content
            // 清理多余的空行
            .replace(/\n\s*\n\s*\n\s*\n/g, '\n\n')
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            
            // 修正缩进问题
            .replace(/^        \}/gm, '    }')
            .replace(/^        \{/gm, '    {')
            
            // 清理方法参数中的多余空格
            .replace(/\(\s*,/g, '(')
            .replace(/,\s*\)/g, ')')
            
            // 修正分号问题
            .replace(/;;\s*$/gm, ';')
            
            .trim();
    }
}

// 使用示例
if (require.main === module) {
    const processor = new PostProcessor();
    
    const args = process.argv.slice(2);
    const targetDir = args[0] || './output_all';
    
    console.log(`📁 目标目录: ${targetDir}\n`);
    processor.processDirectory(targetDir);
}

module.exports = PostProcessor;
