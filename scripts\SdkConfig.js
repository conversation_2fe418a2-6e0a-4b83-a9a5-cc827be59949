var i;
var n;
var r;
var a;
var s;
var c;
var l;
var u;
var p;
var f;
var h;
var d;
var g;
var y;
var m;
var _;
var v;
var M;
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.SoftICP = exports.SoftRightHodler = exports.FarDataSaveList = exports.BMSInfoList = exports.NoSwitchAcount = exports.PayPlatform = exports.ShieldPlatform = exports.TaSdkID = exports.NativePlatform = exports.HWNative = exports.BoxIdList = exports.FeedAdIdList = exports.SplashIdList = exports.InsterIdList = exports.FullVideoIdList = exports.VideoIdList = exports.BannerIdList = exports.AppIdList = exports.SdkClass = exports.Language = exports.EPlatform = undefined;
exports.EPlatform = cc.Enum({
    WEB_DEV: 1,
    NA_ANDROID: 2,
    NA_IOS: 3,
    WECHAT_GAME: 4,
    BYTE_DANCE: 5,
    QQ: 6,
    BAIDU: 7,
    QTT_GAME: 8,
    JKW_GAME: 9,
    OPPO_ANDROID: 10,
    OPPP_MICRO: 11,
    VIVO_MICRO: 12,
    VIVO_ANDROID: 13,
    XIAOMI_ANDROID: 14,
    UC_MICRO: 15,
    GOOGLE_ANDROID: 16,
    XIAOMI_MICRO: 17,
    KWAI_MICRO: 18,
    SSJJ_ANDROID: 19,
    MEIZU_MICRO: 21,
    HUAWEI_MICRO: 22,
    TAPTAP_ANDROID: 23,
    MMY_ANDROID: 24,
    HYKB_ANDROID: 25,
    ANDROID_233: 26,
    AMZ_ANDROID: 27,
    CN_IOS: 28,
    ANDROID_233NXH: 29,
    FXR_ANDROID: 100,
    HHR_ANDROID: 101,
    ALIPAY_MICRO: 105,
    BL_MICRO: 106
});
(function(e) {
    e.zh = "zh";
    e.en = "en";
    e.tw = "tc";
})(exports.Language || (exports.Language = {}));
exports.SdkClass = ((i = {})[exports.EPlatform.WEB_DEV] = "WebDev", i[exports.EPlatform.NA_ANDROID] = "JUHEAndroid", i[exports.EPlatform.TAPTAP_ANDROID] = "JUHEAndroid", i[exports.EPlatform.MMY_ANDROID] = "JUHEAndroid", i[exports.EPlatform.HYKB_ANDROID] = "JUHEAndroid", i[exports.EPlatform.ANDROID_233] = "JUHEAndroid", i[exports.EPlatform.NA_IOS] = "IOSSdk", i[exports.EPlatform.CN_IOS] = "IOSSdk", i[exports.EPlatform.WECHAT_GAME] = "WeChatSdk", i[exports.EPlatform.BYTE_DANCE] = "ByteDance", i[exports.EPlatform.QQ] = "QQSdk", i[exports.EPlatform.BAIDU] = "BaiDuSdk", i[exports.EPlatform.QTT_GAME] = "QttGameSdk", i[exports.EPlatform.JKW_GAME] = "JkwGameSdk", i[exports.EPlatform.OPPO_ANDROID] = "OppoAndroidSdk", i[exports.EPlatform.OPPP_MICRO] = "OppoMicroSdk", i[exports.EPlatform.VIVO_ANDROID] = "VivoAndroidSdk", i[exports.EPlatform.XIAOMI_ANDROID] = "XiaomiAndroidSdk", i[exports.EPlatform.VIVO_MICRO] = "VivoMicroSdk", i[exports.EPlatform.GOOGLE_ANDROID] = "NativeAndroid", i[exports.EPlatform.AMZ_ANDROID] = "NativeAndroid", i[exports.EPlatform.XIAOMI_MICRO] = "XmMicroSdk", i[exports.EPlatform.MEIZU_MICRO] = "MeiZuMicroSdk", i[exports.EPlatform.KWAI_MICRO] = "KwaiSdk", i[exports.EPlatform.SSJJ_ANDROID] = "NativeAndroid", i[exports.EPlatform.HUAWEI_MICRO] = "HuaWeiMicroSdk", i[exports.EPlatform.FXR_ANDROID] = "JUHEAndroid", i[exports.EPlatform.HHR_ANDROID] = "JUHEAndroid", i[exports.EPlatform.ALIPAY_MICRO] = "AlipayMicroSdk", i[exports.EPlatform.BL_MICRO] = "BLMicroSdk", i);
exports.AppIdList = ((n = {})[exports.EPlatform.WEB_DEV] = "wxcd2f1cab37e23eef", n[exports.EPlatform.NA_ANDROID] = "123456", n[exports.EPlatform.FXR_ANDROID] = "123", n[exports.EPlatform.HHR_ANDROID] = "123", n[exports.EPlatform.TAPTAP_ANDROID] = "123456", n[exports.EPlatform.MMY_ANDROID] = "123", n[exports.EPlatform.HYKB_ANDROID] = "123", n[exports.EPlatform.ANDROID_233] = "123", n[exports.EPlatform.NA_IOS] = "6504542031", n[exports.EPlatform.CN_IOS] = "6504542031", n[exports.EPlatform.WECHAT_GAME] = "wx5343c0f8cb04d1ea", n[exports.EPlatform.BYTE_DANCE] = "tt13b1650c8c8780e102", n[exports.EPlatform.QQ] = "1112004175", n[exports.EPlatform.BAIDU] = "24218312", n[exports.EPlatform.QTT_GAME] = "a3XWihw6VsSL", n[exports.EPlatform.JKW_GAME] = "633154511", n[exports.EPlatform.OPPO_ANDROID] = "30535663", n[exports.EPlatform.OPPP_MICRO] = "32429279", n[exports.EPlatform.VIVO_ANDROID] = "105490613", n[exports.EPlatform.VIVO_MICRO] = "105816831", n[exports.EPlatform.XIAOMI_ANDROID] = "2882303761519975991", n[exports.EPlatform.XIAOMI_MICRO] = "2882303761519858998", n[exports.EPlatform.GOOGLE_ANDROID] = "654321", n[exports.EPlatform.AMZ_ANDROID] = "654321", n[exports.EPlatform.KWAI_MICRO] = "ks670473398270522752", n[exports.EPlatform.SSJJ_ANDROID] = "10000", n[exports.EPlatform.HUAWEI_MICRO] = "104520373", n[exports.EPlatform.ALIPAY_MICRO] = "2021004195671503", n[exports.EPlatform.BL_MICRO] = "biligame6981a8aecebe97e2", n);
exports.BannerIdList = ((r = {})[exports.EPlatform.WEB_DEV] = {
    0: "945103842"
}, r[exports.EPlatform.NA_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.FXR_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.HHR_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.TAPTAP_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.HYKB_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.ANDROID_233] = {
    0: "945194818"
}, r[exports.EPlatform.MMY_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.NA_IOS] = {
    0: "10001"
}, r[exports.EPlatform.CN_IOS] = {
    0: "10001"
}, r[exports.EPlatform.WECHAT_GAME] = {
    0: "adunit-bb5e2b5156dc8374"
}, r[exports.EPlatform.BYTE_DANCE] = {
    0: "5jiff6g3hhc98j68fn"
}, r[exports.EPlatform.QQ] = {
    0: "a906545e6c2f50bce79ba7ec72e4599e"
}, r[exports.EPlatform.BAIDU] = {
    0: "7532440-ac9f0292"
}, r[exports.EPlatform.QTT_GAME] = {
    0: "000"
}, r[exports.EPlatform.JKW_GAME] = {
    0: "403716895"
}, r[exports.EPlatform.OPPO_ANDROID] = {
    0: "945194818"
}, r[exports.EPlatform.OPPP_MICRO] = {
    0: "000"
}, r[exports.EPlatform.VIVO_ANDROID] = {
    0: "710e9d4a3bc646bb884ff5a591885264"
}, r[exports.EPlatform.XIAOMI_ANDROID] = {
    0: "f87839e0c8f0cf125086b983ced20b2c"
}, r[exports.EPlatform.VIVO_MICRO] = {
    0: "000"
}, r[exports.EPlatform.SSJJ_ANDROID] = {
    0: "000"
}, r[exports.EPlatform.GOOGLE_ANDROID] = {
    0: "000"
}, r[exports.EPlatform.HUAWEI_MICRO] = {
    0: "000"
}, r[exports.EPlatform.ALIPAY_MICRO] = {
    0: "000"
}, r[exports.EPlatform.BL_MICRO] = {
    0: "000"
}, r);
exports.VideoIdList = ((a = {})[exports.EPlatform.WEB_DEV] = ["945103837"], a[exports.EPlatform.NA_ANDROID] = ["945194814"], a[exports.EPlatform.FXR_ANDROID] = ["945194814"], a[exports.EPlatform.HHR_ANDROID] = ["945194814"], a[exports.EPlatform.TAPTAP_ANDROID] = ["945194814"], a[exports.EPlatform.MMY_ANDROID] = ["945194814"], a[exports.EPlatform.ANDROID_233] = ["945194818"], a[exports.EPlatform.HYKB_ANDROID] = ["945194814"], a[exports.EPlatform.GOOGLE_ANDROID] = ["945194814"], a[exports.EPlatform.AMZ_ANDROID] = ["945194814"], a[exports.EPlatform.NA_IOS] = ["10001"], a[exports.EPlatform.CN_IOS] = ["10001"], a[exports.EPlatform.WECHAT_GAME] = ["adunit-0c7ea3ac849a7237"], a[exports.EPlatform.BYTE_DANCE] = ["6ikpdiko96hn4erawa"], a[exports.EPlatform.QQ] = ["41444a199d0a04083ea980ba25814aa4"], a[exports.EPlatform.BAIDU] = ["7532442-ac9f0292"], a[exports.EPlatform.QTT_GAME] = ["000"], a[exports.EPlatform.JKW_GAME] = ["740329865"], a[exports.EPlatform.OPPO_ANDROID] = ["945194818"], a[exports.EPlatform.OPPP_MICRO] = ["1833054"], a[exports.EPlatform.VIVO_ANDROID] = ["39fcab0f9cb54f66bf92273f791fc364"], a[exports.EPlatform.VIVO_MICRO] = ["efcce02df3434e46801be6baedf1090e"], a[exports.EPlatform.XIAOMI_ANDROID] = ["0b388c102a1a60c4ac78263f286e09a8"], a[exports.EPlatform.XIAOMI_MICRO] = ["43c7cf3f129ebad79a1b91362f3a7f1f"], a[exports.EPlatform.MEIZU_MICRO] = ["4ruNK7xM"], a[exports.EPlatform.KWAI_MICRO] = ["2300001822_01"], a[exports.EPlatform.SSJJ_ANDROID] = ["000"], a[exports.EPlatform.HUAWEI_MICRO] = ["000"], a[exports.EPlatform.ALIPAY_MICRO] = ["ad_tiny_2021004195671503_202411112200203507"], a[exports.EPlatform.BL_MICRO] = ["bili-211732592118564098"], a);
exports.FullVideoIdList = ((s = {})[exports.EPlatform.WEB_DEV] = "000", s[exports.EPlatform.NA_ANDROID] = "945194815", s[exports.EPlatform.FXR_ANDROID] = "945194815", s[exports.EPlatform.HHR_ANDROID] = "945194815", s[exports.EPlatform.TAPTAP_ANDROID] = "945194815", s[exports.EPlatform.MMY_ANDROID] = "945194815", s[exports.EPlatform.HYKB_ANDROID] = "945194815", s[exports.EPlatform.ANDROID_233] = "945194815", s[exports.EPlatform.WECHAT_GAME] = "adunit-0c7ea3ac849a7237", s[exports.EPlatform.GOOGLE_ANDROID] = "945194815", s[exports.EPlatform.AMZ_ANDROID] = "945194815", s[exports.EPlatform.NA_IOS] = "000", s[exports.EPlatform.CN_IOS] = "000", s[exports.EPlatform.BYTE_DANCE] = "6ikpdiko96hn4erawa", s[exports.EPlatform.QQ] = "e3a902fd8fb0231d0179396de8d509b1", s[exports.EPlatform.OPPO_ANDROID] = "945194815", s[exports.EPlatform.OPPP_MICRO] = "000", s[exports.EPlatform.VIVO_ANDROID] = "12eb51a6545d46eab2eb03d663d7a56c", s[exports.EPlatform.XIAOMI_ANDROID] = "f732985ab6265d603140315f81d37a95", s[exports.EPlatform.SSJJ_ANDROID] = "000", s[exports.EPlatform.HUAWEI_MICRO] = "000", s[exports.EPlatform.MEIZU_MICRO] = "fat1dEcr", s[exports.EPlatform.ALIPAY_MICRO] = "000", s[exports.EPlatform.BL_MICRO] = "000", s);
exports.InsterIdList = ((c = {})[exports.EPlatform.WEB_DEV] = "000", c[exports.EPlatform.NA_ANDROID] = "000", c[exports.EPlatform.FXR_ANDROID] = "000", c[exports.EPlatform.HHR_ANDROID] = "000", c[exports.EPlatform.TAPTAP_ANDROID] = "000", c[exports.EPlatform.MMY_ANDROID] = "000", c[exports.EPlatform.WECHAT_GAME] = "adunit-0f0aa8fd4f479fac", c[exports.EPlatform.NA_IOS] = "000", c[exports.EPlatform.CN_IOS] = "000", c[exports.EPlatform.BYTE_DANCE] = "1896g4fd3b4d522fca", c[exports.EPlatform.QQ] = "e3a902fd8fb0231d0179396de8d509b1", c[exports.EPlatform.QTT_GAME] = "000", c[exports.EPlatform.JKW_GAME] = "18753942", c[exports.EPlatform.OPPO_ANDROID] = "000", c[exports.EPlatform.OPPP_MICRO] = "000", c[exports.EPlatform.VIVO_ANDROID] = "12eb51a6545d46eab2eb03d663d7a56c", c[exports.EPlatform.VIVO_MICRO] = "000", c[exports.EPlatform.SSJJ_ANDROID] = "000", c[exports.EPlatform.HUAWEI_MICRO] = "000", c[exports.EPlatform.KWAI_MICRO] = "2300001822_02", c[exports.EPlatform.KWAI_MICRO] = "ad_tiny_2021004195671503_202411112200203487", c[exports.EPlatform.BL_MICRO] = "000", c);
exports.SplashIdList = ((l = {})[exports.EPlatform.WEB_DEV] = "000", l[exports.EPlatform.NA_ANDROID] = "887327489", l[exports.EPlatform.FXR_ANDROID] = "000", l[exports.EPlatform.HHR_ANDROID] = "000", l[exports.EPlatform.TAPTAP_ANDROID] = "887327489", l[exports.EPlatform.MMY_ANDROID] = "887327489", l[exports.EPlatform.NA_IOS] = "000", l[exports.EPlatform.WECHAT_GAME] = "000", l[exports.EPlatform.OPPO_ANDROID] = "887327489", l[exports.EPlatform.OPPP_MICRO] = "000", l[exports.EPlatform.VIVO_ANDROID] = "a7c0cc4a85c847b7915e366680198800", l[exports.EPlatform.XIAOMI_ANDROID] = "887327489", l[exports.EPlatform.SSJJ_ANDROID] = "000", l[exports.EPlatform.HUAWEI_MICRO] = "000", l[exports.EPlatform.ALIPAY_MICRO] = "000", l[exports.EPlatform.BL_MICRO] = "000", l);
exports.FeedAdIdList = ((u = {})[exports.EPlatform.WEB_DEV] = "000", u[exports.EPlatform.NA_ANDROID] = "945194816", u[exports.EPlatform.FXR_ANDROID] = "945194816", u[exports.EPlatform.HHR_ANDROID] = "945194816", u[exports.EPlatform.NA_IOS] = "000", u[exports.EPlatform.WECHAT_GAME] = "000", u[exports.EPlatform.BYTE_DANCE] = "0", u[exports.EPlatform.OPPO_ANDROID] = "945194816", u[exports.EPlatform.OPPP_MICRO] = "1833064", u[exports.EPlatform.VIVO_ANDROID] = "945194816", u[exports.EPlatform.XIAOMI_ANDROID] = "945194816", u[exports.EPlatform.SSJJ_ANDROID] = "000", u[exports.EPlatform.HUAWEI_MICRO] = "000", u[exports.EPlatform.ALIPAY_MICRO] = "000", u[exports.EPlatform.BL_MICRO] = "000", u);
exports.BoxIdList = ((p = {})[exports.EPlatform.QQ] = "4e76c74a420605bb4fa6cc8a03cfe6a5", p[exports.EPlatform.WECHAT_GAME] = "adunit-60f8d0f68ec9eb3d", p);
exports.HWNative = ((f = {})[exports.EPlatform.NA_IOS] = 1, f[exports.EPlatform.GOOGLE_ANDROID] = 1, f[exports.EPlatform.AMZ_ANDROID] = 1, f);
exports.NativePlatform = ((h = {})[exports.EPlatform.NA_ANDROID] = 1, h[exports.EPlatform.FXR_ANDROID] = 1, h[exports.EPlatform.HHR_ANDROID] = 1, h[exports.EPlatform.NA_IOS] = 1, h[exports.EPlatform.OPPO_ANDROID] = 1, h[exports.EPlatform.VIVO_ANDROID] = 1, h[exports.EPlatform.XIAOMI_ANDROID] = 1, h[exports.EPlatform.GOOGLE_ANDROID] = 1, h[exports.EPlatform.SSJJ_ANDROID] = 1, h[exports.EPlatform.TAPTAP_ANDROID] = 1, h[exports.EPlatform.MMY_ANDROID] = 1, h[exports.EPlatform.HYKB_ANDROID] = 1, h[exports.EPlatform.AMZ_ANDROID] = 1, h[exports.EPlatform.CN_IOS] = 1, h[exports.EPlatform.ANDROID_233NXH] = 1, h[exports.EPlatform.ANDROID_233] = 1, h);
exports.TaSdkID = ((d = {})[exports.EPlatform.WECHAT_GAME] = "d10b3f8512db424f93463060082a4f40", d[exports.EPlatform.BYTE_DANCE] = "1e216f3942cf40d483593cdc02ae5c67", d[exports.EPlatform.GOOGLE_ANDROID] = "799512a1b091470f99463842b3fcf4f4", d[exports.EPlatform.WEB_DEV] = "b5598d42cdb5416590344380ad40e936", d[exports.EPlatform.KWAI_MICRO] = "74f3adaf083e41cb88492f7fa8ac13fc", d[exports.EPlatform.CN_IOS] = "b41303fe5408479ca07b18c0a800fb15", d[exports.EPlatform.NA_IOS] = "b41303fe5408479ca07b18c0a800fb15", d[exports.EPlatform.BL_MICRO] = "ab6b30a1a51549cda0c94c56ba973ff2", d);
exports.ShieldPlatform = ((g = {})[exports.EPlatform.WECHAT_GAME] = 1, g[exports.EPlatform.BYTE_DANCE] = 1, g[exports.EPlatform.QQ] = 1, g);
exports.PayPlatform = ((y = {})[exports.EPlatform.GOOGLE_ANDROID] = 1, y[exports.EPlatform.NA_IOS] = 1, y[exports.EPlatform.KWAI_MICRO] = 0, y[exports.EPlatform.WEB_DEV] = 0, y);
exports.NoSwitchAcount = ((m = {})[exports.EPlatform.WECHAT_GAME] = 1, m);
exports.BMSInfoList = ((_ = {})[exports.EPlatform.BYTE_DANCE] = {
    BMS_APP_NAME: "syfxmrtl_tt",
    BMS_VERSION: "2.0.5"
}, _[exports.EPlatform.KWAI_MICRO] = {
    BMS_APP_NAME: "zqxbks",
    BMS_VERSION: "1.0.13"
}, _[exports.EPlatform.WECHAT_GAME] = {
    BMS_APP_NAME: "dlgl_wx",
    BMS_VERSION: "1.0.1",
    CHANNEL_NAME: "微信小游戏"
}, _[exports.EPlatform.WEB_DEV] = {
    BMS_APP_NAME: "dlgl_wx",
    BMS_VERSION: "9.9.9",
    CHANNEL_NAME: "h5"
}, _[exports.EPlatform.NA_ANDROID] = {
    BMS_APP_NAME: "crazyDinosaur_YWAZ",
    BMS_VERSION: "1.0.0",
    CHANNEL_NAME: "自家聚合安卓"
}, _[exports.EPlatform.GOOGLE_ANDROID] = {
    BMS_APP_NAME: "bestsoldiergp",
    BMS_VERSION: "1.0.11"
}, _[exports.EPlatform.AMZ_ANDROID] = {
    BMS_APP_NAME: "fmshymx",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.NA_IOS] = {
    BMS_APP_NAME: "bestsoldierios",
    BMS_VERSION: "1.0.2"
}, _[exports.EPlatform.QQ] = {
    BMS_APP_NAME: "fmshqq",
    BMS_VERSION: "1.0.2"
}, _[exports.EPlatform.BAIDU] = {
    BMS_APP_NAME: "fmshbd",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.QTT_GAME] = {
    BMS_APP_NAME: "dmxqtt",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.JKW_GAME] = {
    BMS_APP_NAME: "dmxjkw",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.OPPO_ANDROID] = {
    BMS_APP_NAME: "shmnqoppoapk",
    BMS_VERSION: "1.0.3"
}, _[exports.EPlatform.OPPP_MICRO] = {
    BMS_APP_NAME: "zqxb_oppo",
    BMS_VERSION: "1.0.0"
}, _[exports.EPlatform.VIVO_MICRO] = {
    BMS_APP_NAME: "zqxbvivo",
    BMS_VERSION: "1.0.2"
}, _[exports.EPlatform.VIVO_ANDROID] = {
    BMS_APP_NAME: "fmshvivoapk",
    BMS_VERSION: "1.0.0"
}, _[exports.EPlatform.XIAOMI_ANDROID] = {
    BMS_APP_NAME: "fmshxmapk",
    BMS_VERSION: "1.0.1"
}, _[exports.EPlatform.XIAOMI_MICRO] = {
    BMS_APP_NAME: "fmshxm",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.MEIZU_MICRO] = {
    BMS_APP_NAME: "fmshmz",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.SSJJ_ANDROID] = {
    BMS_APP_NAME: "fmsh4399",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.TAPTAP_ANDROID] = {
    BMS_APP_NAME: "fmshtaptap",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.MMY_ANDROID] = {
    BMS_APP_NAME: "fmshmmy",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.HYKB_ANDROID] = {
    BMS_APP_NAME: "fmshhykb",
    BMS_VERSION: "1.4.0"
}, _[exports.EPlatform.ANDROID_233] = {
    BMS_APP_NAME: "fmsh233apk",
    BMS_VERSION: "1.0.0"
}, _[exports.EPlatform.CN_IOS] = {
    BMS_APP_NAME: "fmshgnios",
    BMS_VERSION: "1.0.6"
}, _[exports.EPlatform.HUAWEI_MICRO] = {
    BMS_APP_NAME: "fmshhwkyx",
    BMS_VERSION: "1.0.0"
}, _[exports.EPlatform.ALIPAY_MICRO] = {
    BMS_APP_NAME: "zqxbbfzfb",
    BMS_VERSION: "1.0.0"
}, _[exports.EPlatform.BL_MICRO] = {
    BMS_APP_NAME: "zqxbbilibili",
    BMS_VERSION: "1.0.0"
}, _);
exports.FarDataSaveList = [exports.EPlatform.BYTE_DANCE, exports.EPlatform.WECHAT_GAME, exports.EPlatform.WEB_DEV];
exports.SoftRightHodler = ((v = {})[exports.EPlatform.WEB_DEV] = "", v[exports.EPlatform.NA_ANDROID] = "", v[exports.EPlatform.TAPTAP_ANDROID] = "", v[exports.EPlatform.MMY_ANDROID] = "", v[exports.EPlatform.HYKB_ANDROID] = "", v[exports.EPlatform.ANDROID_233] = "", v[exports.EPlatform.NA_IOS] = "", v[exports.EPlatform.CN_IOS] = "", v[exports.EPlatform.WECHAT_GAME] = "", v[exports.EPlatform.BYTE_DANCE] = "", v[exports.EPlatform.QQ] = "", v[exports.EPlatform.BAIDU] = "", v[exports.EPlatform.QTT_GAME] = "", v[exports.EPlatform.JKW_GAME] = "", v[exports.EPlatform.OPPO_ANDROID] = "", v[exports.EPlatform.OPPP_MICRO] = "", v[exports.EPlatform.VIVO_ANDROID] = "", v[exports.EPlatform.XIAOMI_ANDROID] = "", v[exports.EPlatform.VIVO_MICRO] = "", v[exports.EPlatform.GOOGLE_ANDROID] = "", v[exports.EPlatform.AMZ_ANDROID] = "", v[exports.EPlatform.XIAOMI_MICRO] = "", v[exports.EPlatform.MEIZU_MICRO] = "", v[exports.EPlatform.KWAI_MICRO] = "", v[exports.EPlatform.SSJJ_ANDROID] = "", v[exports.EPlatform.HUAWEI_MICRO] = "", v[exports.EPlatform.FXR_ANDROID] = "", v[exports.EPlatform.HHR_ANDROID] = "", v[exports.EPlatform.ALIPAY_MICRO] = "", v[exports.EPlatform.BL_MICRO] = "", v);
exports.SoftICP = ((M = {})[exports.EPlatform.WEB_DEV] = "", M[exports.EPlatform.NA_ANDROID] = "", M[exports.EPlatform.TAPTAP_ANDROID] = "", M[exports.EPlatform.MMY_ANDROID] = "", M[exports.EPlatform.HYKB_ANDROID] = "", M[exports.EPlatform.ANDROID_233] = "", M[exports.EPlatform.NA_IOS] = "", M[exports.EPlatform.CN_IOS] = "", M[exports.EPlatform.WECHAT_GAME] = "", M[exports.EPlatform.BYTE_DANCE] = "粤ICP备19160116号-135X", M[exports.EPlatform.QQ] = "", M[exports.EPlatform.BAIDU] = "", M[exports.EPlatform.QTT_GAME] = "", M[exports.EPlatform.JKW_GAME] = "", M[exports.EPlatform.OPPO_ANDROID] = "", M[exports.EPlatform.OPPP_MICRO] = "", M[exports.EPlatform.VIVO_ANDROID] = "", M[exports.EPlatform.XIAOMI_ANDROID] = "", M[exports.EPlatform.VIVO_MICRO] = "", M[exports.EPlatform.GOOGLE_ANDROID] = "", M[exports.EPlatform.AMZ_ANDROID] = "", M[exports.EPlatform.XIAOMI_MICRO] = "", M[exports.EPlatform.MEIZU_MICRO] = "", M[exports.EPlatform.KWAI_MICRO] = "", M[exports.EPlatform.SSJJ_ANDROID] = "", M[exports.EPlatform.HUAWEI_MICRO] = "", M[exports.EPlatform.FXR_ANDROID] = "", M[exports.EPlatform.HHR_ANDROID] = "", M[exports.EPlatform.ALIPAY_MICRO] = "", M[exports.EPlatform.BL_MICRO] = "", M);