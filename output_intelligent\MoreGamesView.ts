import $2GridView from "./GridView";
import $2VideoButton from "./VideoButton";
import $2Cfg from "./Cfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Pop from "./Pop";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeChainsModel from "./ModeChainsModel";
import $2MoreGamesItem from "./MoreGamesItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MoreGamesView extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
