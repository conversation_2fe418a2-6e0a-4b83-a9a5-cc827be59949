import $2CallID from "./CallID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class VideoButton extends cc.Component {
    // TODO: 添加属性和方法
}