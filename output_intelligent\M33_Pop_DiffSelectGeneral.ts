import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M33_Pop_DiffSelectGeneral extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
