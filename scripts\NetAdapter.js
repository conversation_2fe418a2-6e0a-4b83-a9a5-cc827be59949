Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NetAdapter = undefined;
var $2NetManager = require("NetManager");
var $2Manager = require("Manager");
var r = function () {
  function e() {
    this.serverDomain = "";
    this._getInfo = "/common/archive/info";
    this._saveInfo = "/common/archive/save";
    this._createOrder = "/common/wj-order/create";
    this._confirmOrder = "/common/wj-order/consume";
    this._checkOrder = "/common/wj-order/get";
    this._getOderInfo = "/common/wj-order/get-unconsumed";
    this._keyId = "";
    this._appName = "";
    this._version = "";
    this._channel = "";
  }
  e.prototype.setUuid = function (e) {
    this._keyId = e;
  };
  Object.defineProperty(e.prototype, "uuid", {
    get: function () {
      return this._keyId;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "gameDomain", {
    get: function () {
      if (wonderSdk.isTest || $2Manager.Manager.vo.switchVo.isCheck) {
        return "";
      } else {
        return "";
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "loginDomain", {
    get: function () {
      return this.gameDomain + "login";
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.setAppName = function (e) {
    this._appName = e;
  };
  e.prototype.setVersion = function (e) {
    this._version = e;
  };
  e.prototype.setChannel = function (e) {
    this._channel = e;
  };
  e.prototype.getUserData = function () {
    return this.httpRequest(this.domain + this._getInfo, {
      app_name: this._appName,
      uuid: this._keyId
    }, "POST");
  };
  e.prototype.uploadData = function (e) {
    return this.httpRequest(this.domain + this._saveInfo, {
      app_name: this._appName,
      uuid: this._keyId,
      data: e
    }, "POST");
  };
  e.prototype.createOrder = function () {
    return this.httpRequest(this.domain + this._createOrder, {
      app_name: this._appName,
      channel: this._channel,
      version: this._version
    }, "GET");
  };
  e.prototype.confirmOrder = function (e) {
    return this.httpRequest(this.domain + this._confirmOrder, {
      app_name: this._appName,
      channel: this._channel,
      version: this._version,
      order_no: e
    }, "GET");
  };
  e.prototype.getOrder = function (e, t) {
    var o = {
      app_name: this._appName,
      channel: this._channel,
      version: this._version
    };
    if (t) {
      Object.assign(o, {
        order_no: e
      });
    } else {
      Object.assign(o, {
        game_order_no: e
      });
    }
    return this.httpRequest(this.domain + this._checkOrder, o, "GET");
  };
  e.prototype.getCheckOrder = function () {
    var e = {
      app_name: this._appName,
      channel: this._channel,
      version: this._version,
      wj_uid: this._keyId
    };
    return this.httpRequest(this.domain + this._getOderInfo, e, "POST");
  };
  Object.defineProperty(e.prototype, "domain", {
    get: function () {
      return this.serverDomain;
    },
    enumerable: false,
    configurable: true
  });
  e.prototype.httpRequest = function (e, t, o, n) {
    undefined === n && (n = "json");
    return $2NetManager.NetManager.instance.httpRequest(e, t, o, n);
  };
  Object.defineProperty(e.prototype, "socket", {
    get: function () {
      return $2NetManager.NetManager.instance.socket;
    },
    enumerable: false,
    configurable: true
  });
  return e;
}();
exports.NetAdapter = new r();