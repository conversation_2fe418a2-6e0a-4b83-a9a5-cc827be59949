import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2DropConfigCfg from "./DropConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2Game from "./Game";
import $2TrackManger from "./TrackManger";
import $2BaseEntity from "./BaseEntity";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class Goods extends $2BaseEntity.default {
    // TODO: 添加属性和方法
}