import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2ModeChainsModel from "./ModeChainsModel";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Activity")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M20_PrePare_Activity extends $2Pop.Pop {
    get mode() {
        return $2ModeChainsModel.default.instance;
    }

    setInfo() {
        this.nodeArr[0].getChildByName("MoreGames").zIndex = 999;
            this.nodeArr[1].active = 1 == $2Manager.Manager.vo.switchVo.GameKnife;
    }

    onEnable() {
        this.resetState();
    }

    resetState() {
        const e = this;
            this.unscheduleAllCallbacks();
            const t = 0;
            $2Cfg.Cfg.activity.forEach(function (o) {
              e.setItem(e.mode.rVo.activityList[o.id], t);
              t++;
            });
    }

    setItem(e: any, t: any) {
        const o = $2Cfg.Cfg.activity.get(e.id);
            const i = this.nodeArr[0].filterChild({
              name: "item"
            })[t] || cc.instantiate(this.nodeArr[0].children[0]).setAttribute({
              parent: this.nodeArr[0]
            });
            i.getComByPath(cc.Label, "name").string = o.actName;
            i.zIndex = o.unlockChapter;
            const n = i.getChildByName("btn");
            n.targetOff(this);
            n.on(cc.Node.EventType.TOUCH_END, function () {
              if ($2ModeBackpackHeroModel.default.instance.userEquipPack.filter(function (e) {
                return e.isFitOut;
              }).length < 8) {
                return $2AlertManager.AlertManager.showNormalTips("装备数量不足");
              } else {
                if (0 == e.cgNum) {
                  return $2AlertManager.AlertManager.showNormalTips("挑战次数不足");
                } else {
                  return void $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
                    type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
                    val: $2Manager.Manager.vo.switchVo.fightStamina,
                    call: function (t) {
                      if (t == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
                        e.cgNum--;
                        const i = $2Game.Game.getMouth($2Game.Game.Mode.CHAINS);
                        $2Notifier.Notifier.send(i.mouth, $2Game.Game.Mode.CHAINS, $2MVC.MVC.openArgs().setParam({
                          id: o.lvId
                        }));
                      }
                    }
                  });
                }
              }
            }, this);
            $2Manager.Manager.loader.loadSpriteToSprit(o.actIcon, i.getComponent(cc.Sprite));
            const r = i.getChildByName("rewardList");
            o.reward.forEach(function (e, t) {
              const o = $2Cfg.Cfg.CurrencyConfig.get(e);
              const i = r.children[t] || cc.instantiate(r.children[0]).setAttribute({
                parent: r
              });
              $2Manager.Manager.loader.loadSpriteToSprit(o.icon, i.getComByChild(cc.Sprite, "icon"));
              $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(o.rarity).blockImg, i.getComponent(cc.Sprite));
            });
            r.spliceNode(o.reward.length, 10);
            const p = function () {
              i.getComByChild(cc.Label, "time").string = cc.js.formatStr("剩余时间:%s", $2GameUtil.GameUtil.formatSeconds(($2Time.Time.midnight - $2Time.Time.serverTimeMs) / 1e3).str);
              i.getComByPath(cc.Label, "mask/lockMsg").string = cc.js.formatStr("通关第%d章解锁", o.unlockChapter);
              i.getComByChild(cc.Label, "num").string = cc.js.formatStr("今日剩余挑战次数:%d", e.cgNum);
              i.getChildByName("mask").setActive($2Manager.Manager.leveMgr.vo.curPassLv < o.unlockChapter);
            };
            p();
            this.schedule(p, 1);
    }

    onShowFinish() {
        let e: any;
            let t: any;
            null === (t = null === (e = this.param) || e === undefined ? undefined : e.showCb) || t === undefined || t.call(e, this.node);
            this.node.opacity = 255;
    }

    onOpen() {
        this.node.opacity = 0;
    }

    getThis() {
        return this;
    }

    onBtn(e: any, t: any) {
        if (t.includes("ui/")) {
              const o = $2MVC.MVC.openArgs();
              if ("ui/setting/MoreGamesView" == t) {
                const i = $2MVC.MVC.openArgs();
                i.setParam({
                  pageIndex: $2Game.Game.Mode.CHAINS
                });
                return void $2UIManager.UIManager.Open("ui/setting/MoreGamesView", i);
              }
              $2UIManager.UIManager.OpenInQueue(t, o);
            }
    }

}
