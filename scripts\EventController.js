var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EventController = undefined;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2SdkConfig = require("SdkConfig");
var $2WonderSdk = require("WonderSdk");
var $2EventModel = require("EventModel");
var exp_EventController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t._issetLoading = 0;
    t.thinkingAnalyticsAPI = null;
    t.setup($2EventModel.default.instance);
    t.changeListener(true);
    t.initEventSdk();
    t._issetLoading = $2Manager.Manager.storage.getNumber("loadingStep", 0);
    t.setSuperProperties({
      ditch_Name: wonderSdk.CHANNEL_NAME
    });
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "EventController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Event_SendEvent, this.onSendEvent, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Event_SetUserProperty, this.setUserProperty, this);
    if (e) {
      cc.game.on("Event_SetUserProperty", this.setUserProperty, this);
    } else {
      cc.game.off("Event_SetUserProperty", this.setUserProperty, this);
    }
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Event_SetSuperProperties, this.setSuperProperties, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Event_LoginTA, this.loginTa, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Event_LogOutTA, this.logOutTa, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.loginFinish, this);
  };
  _ctor.prototype.loginFinish = function () {
    this.setUserProperty("user_setOnce", {
      InitialGameVersion: wonderSdk.BMS_VERSION
    });
  };
  _ctor.prototype.onSendEvent = function (e, t) {
    if (!("reward_btn" == e && "success" != t.Type)) {
      console.log("[SendEvent] " + e + "  " + JSON.stringify(t));
      this.thinkingAnalyticsAPI && this.thinkingAnalyticsAPI.track(e, t);
    }
  };
  _ctor.prototype.setSuperProperties = function (e) {
    this.thinkingAnalyticsAPI && this.thinkingAnalyticsAPI.setSuperProperties(e);
  };
  _ctor.prototype.setUserProperty = function (e, t) {
    if (t) {
      if ("object" == typeof t) {
        if (this.thinkingAnalyticsAPI) {
          if ("user_set" == e) {
            this.thinkingAnalyticsAPI.userSet(t);
          } else if ("user_add" == e) {
            this.thinkingAnalyticsAPI.userAdd(t);
          } else {
            "user_setOnce" == e && this.thinkingAnalyticsAPI.userSetOnce(t);
          }
        }
      } else {
        cc.error("setProperty is not a object", t);
      }
    }
  };
  _ctor.prototype.initEventSdk = function () {
    cc.log("taid", $2SdkConfig.TaSdkID[$2WonderSdk.WonderSdk._instance.platformId]);
    if ($2SdkConfig.TaSdkID[$2WonderSdk.WonderSdk._instance.platformId]) {
      var e = {
        appId: $2SdkConfig.TaSdkID[$2WonderSdk.WonderSdk._instance.platformId],
        serverUrl: "",
        enableNative: true,
        autoTrack: {
          appShow: true,
          appHide: true,
          appClick: true,
          appView: true,
          appCrash: true,
          appInstall: true
        },
        enableLog: false
      };
      // this.thinkingAnalyticsAPI = new window.ThinkingAnalyticsAPI(e);
      // this.thinkingAnalyticsAPI.init();
    }
  };
  _ctor.prototype.loginTa = function (e) {
    if (this.thinkingAnalyticsAPI) {
      this.thinkingAnalyticsAPI.login(e);
      this.mode.userVo.userId = e;
      this.mode.userVo.version = $2SdkConfig.BMSInfoList[wonderSdk.platformId].BMS_VERSION;
      console.log("[TaEventController.userVo]", this.mode.userVo);
      this.setUserProperty("user_set", this.mode.userVo);
      this.setSuperProperties(this.mode.userVo);
      $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "user_Login", {
        ad_id: this.mode.userVo.ad_id,
        userId: e
      });
      this.setUserProperty("user_set", {
        receiveLiveGift: $2Manager.Manager.storage.getString("record_live_gift_code", "")
      });
    } else {
      console.log("loginTa init fail");
    }
  };
  _ctor.prototype.logOutTa = function () {
    this.thinkingAnalyticsAPI && this.thinkingAnalyticsAPI.logout();
  };
  _ctor.prototype.getPresetProperties = function () {
    if (this.thinkingAnalyticsAPI) {
      var e = this.thinkingAnalyticsAPI.getPresetProperties().toEventPresetProperties();
      e.client_version = $2SdkConfig.BMSInfoList[wonderSdk.platformId].BMS_VERSION;
      return JSON.stringify(e);
    }
    return null;
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.EventController = exp_EventController;