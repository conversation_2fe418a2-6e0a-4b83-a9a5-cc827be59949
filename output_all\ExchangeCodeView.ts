import $2CallID from "./CallID";
import $2Listen<PERSON> from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2WonderSdk from "./WonderSdk";
import $2BaseNet from "./BaseNet";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class ExchangeCodeView extends $2Pop.Pop {
    // TODO: 添加属性和方法
}