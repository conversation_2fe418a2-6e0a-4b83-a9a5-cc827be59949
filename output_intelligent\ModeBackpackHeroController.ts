import $2CallID from "./CallID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2BaseNet from "./BaseNet";
import $2WonderSdk from "./WonderSdk";
import $2KnapsackVo from "./KnapsackVo";
import $2EventModel from "./EventModel";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

export default class ModeBackpackHeroController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}
