# 转换失败的文件列表

## 概述

在批量转换过程中，有 3 个文件转换失败。这些文件失败的原因是它们不符合标准的 Cocos Creator 类定义模式，需要特殊处理。

**转换统计**: 40/43 成功 (93% 成功率)

## 失败文件详情

### 1. AlertManager.js
**失败原因**: 静态类/命名空间模式，不是标准的类定义

**原始结构**:
```javascript
var exp_AlertManager = function () {
  function _ctor() {}
  _ctor.showAlert = function (e, t) { ... }
  _ctor.showSelectAlert = function (e) { ... }
  _ctor.showNormalTips = function (e, t) { ... }
  // ... 更多静态方法
  return _ctor;
}();
```

**建议的 TypeScript 转换**:
```typescript
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2DialogBox from "./DialogBox";
import $2Game from "./Game";
import $2NodePool from "./NodePool";
import $2SettingModel from "./SettingModel";
import $2NormalTips from "./NormalTips";

export enum AlertType {
    COMMON = "0",
    SELECT = "1"
}

export enum ItemAlertType {
    Tips = 0,
    NotEnough = 1
}

export class AlertManager {
    static showAlert(type: any, params: any): void {
        // 实现内容
    }
    
    static showSelectAlert(params: any): void {
        // 实现内容
    }
    
    static showNormalTips(message: string, duration?: number): void {
        // 实现内容
    }
    
    // ... 其他静态方法
}
```

### 2. Api.js
**失败原因**: 函数模块，导出多个独立函数

**原始结构**:
```javascript
exports.click = function (e, t, o, r) { ... };
exports.report = function (e, t, o, a) { ... };
exports.getOpenid = function (e, t, o, r) { ... };
exports.serverTime = function (e, t, o, r) { ... };
```

**建议的 TypeScript 转换**:
```typescript
import $2Request from "./Request";
import $2config from "./config";
import $2md51 from "./md51";
import $2SdkConfig from "./SdkConfig";

export function click(
    data: any, 
    success?: () => void, 
    fail?: () => void, 
    complete?: () => void
): void {
    success = success || (() => {});
    fail = fail || (() => {});
    complete = complete || (() => {});
    
    $2Request.default.post({
        url: $2config.DOMAIN + "/common/app-track/click",
        data: data,
        dataType: "json",
        success: success,
        fail: fail,
        complete: complete
    });
}

export function report(
    data: any,
    success?: () => void,
    fail?: () => void,
    complete?: () => void
): void {
    // 实现内容
}

export function getOpenid(
    data: any,
    success?: () => void,
    fail?: () => void,
    complete?: () => void
): void {
    // 实现内容
}

export function serverTime(
    data: any,
    success?: () => void,
    fail?: () => void,
    complete?: () => void
): void {
    // 实现内容
}
```

### 3. AssetLoader.js
**失败原因**: 类定义但无继承关系

**原始结构**:
```javascript
var exp_LoadResArgs = function () {};
exports.LoadResArgs = exp_LoadResArgs;

var def_AssetLoader = function () {
  function _ctor() {
    this._resKeeper = null;
  }
  _ctor.makeLoadResArgs = function () { ... }
  // ... 更多方法
  return _ctor;
}; // 注意：没有继承关系的 (baseClass)
```

**建议的 TypeScript 转换**:
```typescript
import $2Log from "./Log";
import $2ResKeeper from "./ResKeeper";

export class LoadResArgs {
    // 根据需要添加属性
}

export class AssetLoader {
    private _resKeeper: any = null;
    
    static makeLoadResArgs(): LoadResArgs | null {
        if (arguments.length < 1) {
            $2Log.Log.error("_makeLoadResArgs error " + arguments);
            return null;
        }
        
        if (arguments.length === 1 && arguments[0] instanceof LoadResArgs) {
            return arguments[0];
        }
        
        // 实现其余逻辑
        return null;
    }
    
    // ... 其他方法
}

export const assetLoader = AssetLoader;
```

## 手动转换指南

### 步骤 1: 分析文件结构
1. 确定文件是类、静态类还是函数模块
2. 识别导入的依赖模块
3. 找出导出的内容

### 步骤 2: 转换导入语句
```javascript
var $2Module = require("Module");
```
转换为:
```typescript
import $2Module from "./Module";
```

### 步骤 3: 转换导出内容
- **类**: 使用 `export default class` 或 `export class`
- **函数**: 使用 `export function`
- **枚举**: 使用 `export enum`
- **常量**: 使用 `export const`

### 步骤 4: 添加类型注解
- 为函数参数添加类型
- 为返回值添加类型
- 为类属性添加类型

## 自动化改进建议

如果需要支持这些特殊模式，可以扩展转换脚本：

1. **静态类检测**: 检测 `function () { ... return _ctor; }()` 模式
2. **函数模块检测**: 检测 `exports.functionName = function` 模式
3. **无继承类检测**: 检测没有 `}(baseClass);` 结尾的类定义

## 文件位置

失败的原始文件位于:
- `scripts/AlertManager.js`
- `scripts/Api.js` 
- `scripts/AssetLoader.js`

建议手动转换后放置在:
- `output/AlertManager.ts`
- `output/Api.ts`
- `output/AssetLoader.ts`
