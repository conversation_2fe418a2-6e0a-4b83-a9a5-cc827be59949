const fs = require('fs');

function fixSuperCalls(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error('文件不存在:', filePath);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 修正各种父类调用模式
    content = content
        // 修正 this.prototype.method.call(this, args) -> super.method(args)
        .replace(/this\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\);/g, 'super.$1($2);')
        .replace(/this\.prototype\.(\w+)\.call\(this\);/g, 'super.$1();')
        
        // 修正 e.prototype.method.call(this, args) -> super.method(args)
        .replace(/e\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\);/g, 'super.$1($2);')
        .replace(/e\.prototype\.(\w+)\.call\(this\);/g, 'super.$1();')
        
        // 修正 t.prototype.method.call(this, args) -> super.method(args)
        .replace(/t\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\);/g, 'super.$1($2);')
        .replace(/t\.prototype\.(\w+)\.call\(this\);/g, 'super.$1();')
        
        // 修正变量声明
        .replace(/var e = this;/g, '')
        .replace(/var t = this;/g, '')
        .replace(/var o = this;/g, '')
        .replace(/var i = this;/g, '')
        .replace(/var n = this;/g, '')
        
        // 清理多余的空行
        .replace(/\n\s*\n\s*\n/g, '\n\n');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ 父类调用修正完成:', filePath);
}

// 使用示例
if (require.main === module) {
    const args = process.argv.slice(2);
    const filePath = args[0] || './output/BuffList_final.ts';
    
    fixSuperCalls(filePath);
}

module.exports = { fixSuperCalls };
