import $2CallID from "./CallID";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2Game from "./Game";
import $2BaseSdk from "./BaseSdk";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class ShareButton extends cc.Component {
    // TODO: 添加属性和方法
}