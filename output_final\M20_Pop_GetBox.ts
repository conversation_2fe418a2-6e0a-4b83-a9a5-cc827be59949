import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Gooditem from "./M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_GetBox")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
@$2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Popup)
export default class M20_Pop_GetBox extends $2Pop.Pop {
    @property(cc.Label)
    boxname: cc.Label = null;

    @property(cc.Label)
    tips: cc.Label = null;

    @property(cc.Node)
    box: cc.Node = null;

    @property(cc.Node)
    shine: cc.Node = null;

    @property(cc.Sprite)
    boximg: cc.Sprite = null;

    @property(cc.Node)
    rewardContainer: cc.Node = null;

    constructor() {
        super();
        this.clicktims = 0;
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(t: any) {
        super.changeListener(t);
    }

    onOpen() {
        let e: any;
            $2Manager.Manager.loader.loadSpriteToSprit("v1/images/icon/good" + this.param.data.id.toString().slice(0, 3) + "01", this.boximg);
            this.tips.string = "打开宝箱";
            cc.tween(this.shine).by(.2, {
              angle: 10
            }).repeatForever().start();
            this.boxname.string = null === (e = this.param) || e === undefined ? undefined : e.data.title;
            cc.tween(this.box).set({
              y: 1e3
            }).to(.2, {
              y: 0
            }).start();
            cc.tween(this.box).delay(.2).by(.2, {
              y: 100
            }).by(.2, {
              y: -100
            }).delay($2GameUtil.GameUtil.random(1, 3)).union().repeatForever().start();
    }

    getReward() {
        return cc__awaiter(this, undefined, undefined, function () {
              let e: any;
              let t: any;
              let o: any;
              let i: any;
              const n = this;
              return cc__generator(this, function (r) {
                switch (r.label) {
                  case 0:
                    if (this.clicktims >= 1) {
                      return 2 == this.clicktims && this.close(), [2];
                    } else {
                      return e = this.mode.fragments, this.clicktims++, this.tips.string = "", cc.Tween.stopAllByTarget(this.box), t = $2GameUtil.GameUtil.random(10, 30), cc.tween(this.box).to(.2, {
                        angle: -t
                      }).to(.2, {
                        angle: t
                      }).to(.1, {
                        angle: 0
                      }).delay(.3).to(.5, {
                        opacity: 0
                      }).call(function () {
                        n.rewardContainer.setActive(true);
                        n.tips.string = "点击关闭";
                        n.clicktims = 2;
                      }).start(), this.param.data ? [4, $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (e) {
                        o = e;
                      })] : [3, 2];
                    }
                  case 1:
                    r.sent();
                    i = 0;
                    this.param.data.boxReward.forEach(function (t) {
                      const r = t[1];
                      const a = t[0];
                      const s = {
                        count: r,
                        path: "",
                        bgpath: "",
                        isfrag: false
                      };
                      if (e.includes(a)) {
                        n.mode.getRandomFragById(t).forEach(function (e, t) {
                          const r = $2Cfg.Cfg.RoleUnlock.get(t);
                          s.path = r.icon;
                          s.count = e;
                          s.bgpath = "v1/images/bg/bg_icon_0" + r.rarity;
                          s.isfrag = true;
                          n.mode.addFragment(r.id, e);
                          const a = cc.instantiate(o);
                          a.setParent(n.rewardContainer);
                          a.getComponent($2M20Gooditem.default).setdata(s);
                          cc.tween(a).set({
                            opacity: 0
                          }).delay(1.2 + .1 * i).to(.2, {
                            opacity: 255
                          }).start();
                          i++;
                        });
                      } else {
                        const c = cc.instantiate(o);
                        c.setParent(n.rewardContainer);
                        const u = $2Cfg.Cfg.CurrencyConfig.get(a);
                        s.path = u.icon;
                        $2Manager.Manager.vo.knapsackVo.addGoods(a, r);
                        $2AlertManager.AlertManager.showNormalTips("获得" + u.name + "x" + r);
                        c.getComponent($2M20Gooditem.default).setdata(s);
                        cc.tween(c).set({
                          opacity: 0
                        }).delay(1.2 + .1 * i).to(.2, {
                          opacity: 255
                        }).start();
                        i++;
                      }
                    });
                    r.label = 2;
                  case 2:
                    return [2];
                }
              });
            });
    }

    onBtn(e: any, t: any) {
        $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView).onBtn(e, t);
    }

    onClose() {
        let e: any;
            let t: any;
            cc.Tween.stopAllByTarget(this.shine);
            cc.Tween.stopAllByTarget(this.box);
            null === (t = null === (e = this.param) || e === undefined ? undefined : e.cb) || t === undefined || t.call(e);
    }

}
