const fs = require('fs');
const path = require('path');

class MultiClassConverter {
    constructor() {
        this.importMap = new Map();
    }

    // 转换多类结构的 JS 文件
    convertMultiClassFile(inputPath, outputPath) {
        try {
            const jsContent = fs.readFileSync(inputPath, 'utf8');
            const tsContent = this.convertMultiClassJS(jsContent);
            
            if (tsContent) {
                fs.writeFileSync(outputPath, tsContent, 'utf8');
                console.log(`✅ 多类转换成功: ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
                return true;
            } else {
                console.log(`❌ 多类转换失败: ${path.basename(inputPath)}`);
                return false;
            }
        } catch (error) {
            console.error(`❌ 多类转换出错 ${path.basename(inputPath)}:`, error.message);
            return false;
        }
    }

    // 主转换方法
    convertMultiClassJS(content) {
        // 提取导入语句
        const imports = this.extractImports(content);
        
        // 提取所有类定义
        const classes = this.extractAllClasses(content);
        
        if (!classes || classes.length === 0) {
            console.log('❌ 未找到类定义');
            return null;
        }

        console.log(`📦 发现 ${classes.length} 个类:`);
        classes.forEach(cls => {
            console.log(`   - ${cls.className} extends ${cls.baseClass || 'undefined'}`);
        });

        // 生成 TypeScript 代码
        let tsCode = '';
        
        // 添加导入语句
        if (imports.length > 0) {
            tsCode += imports.join('\n') + '\n\n';
        }
        
        // 添加装饰器导入
        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';
        
        // 生成所有类
        classes.forEach((classInfo, index) => {
            tsCode += this.generateClassCode(classInfo);
            if (index < classes.length - 1) {
                tsCode += '\n';
            }
        });

        return tsCode;
    }

    // 提取导入语句
    extractImports(content) {
        const imports = [];
        const regex = /var (\$\d*\$?\d*\w+) = require\("(\w+)"\);/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            const moduleName = match[1];
            const fileName = match[2];
            imports.push(`import ${moduleName} from "./${fileName}";`);
            this.importMap.set(moduleName, moduleName);
        }

        return imports;
    }

    // 提取所有类定义
    extractAllClasses(content) {
        const classes = [];

        // 分割内容，找到每个类的定义
        const lines = content.split('\n');
        let currentClass = null;
        let classContent = [];
        let braceCount = 0;
        let inClass = false;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // 检测类定义开始
            const classMatch = line.match(/var (exp_\w+) = function \(e\) \{/);
            if (classMatch) {
                // 如果之前有类，先处理它
                if (currentClass) {
                    this.processClass(currentClass, classContent, classes);
                }

                // 开始新类
                currentClass = {
                    fullVarName: classMatch[1],
                    className: classMatch[1].replace('exp_', ''),
                    startLine: i
                };
                classContent = [line];
                braceCount = 1;
                inClass = true;
                continue;
            }

            if (inClass) {
                classContent.push(line);

                // 计算大括号
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                // 检查是否到了类定义结束
                if (braceCount === 0 && line.includes('}(')) {
                    // 提取基类 - 支持多种格式
                    let baseMatch = line.match(/\}(\(\$\d+\w+(?:\.\w+)*\));/);
                    if (baseMatch) {
                        currentClass.baseClass = baseMatch[1].replace(/[()]/g, '');
                    } else {
                        // 检查是否继承自其他 exp_ 类
                        baseMatch = line.match(/\}(exp_\w+);/);
                        if (baseMatch) {
                            currentClass.baseClass = baseMatch[1].replace('exp_', '');
                        } else {
                            // 检查带括号的 exp_ 类继承
                            baseMatch = line.match(/\}\((exp_\w+)\);/);
                            if (baseMatch) {
                                currentClass.baseClass = baseMatch[1].replace('exp_', '');
                            }
                        }
                    }

                    // 查找对应的 exports 行
                    for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
                        if (lines[j].includes(`exports.${currentClass.fullVarName} = ${currentClass.fullVarName};`)) {
                            currentClass.endLine = j;
                            break;
                        }
                    }

                    this.processClass(currentClass, classContent, classes);
                    currentClass = null;
                    classContent = [];
                    inClass = false;
                }
            }
        }

        return classes;
    }

    // 处理单个类
    processClass(classInfo, classContent, classes) {
        const fullContent = classContent.join('\n');
        const classDetails = this.extractClassDetails(fullContent, classInfo.fullVarName);

        classes.push({
            ...classInfo,
            classContent: fullContent,
            ...classDetails
        });
    }

    // 提取类的详细信息
    extractClassDetails(classContent, fullVarName) {
        const details = {
            constructor: null,
            methods: [],
            properties: [],
            decorators: []
        };

        // 提取装饰器信息
        const decoratorMatch = classContent.match(/cc__decorate\(\[ccp_ccclass\("([^"]+)"\)\]/);
        if (decoratorMatch) {
            details.decorators.push('@ccclass');
        }

        // 提取构造函数 - 查找 function _ctor() 或 function t()
        const constructorMatch = classContent.match(/function ([_\w]+)\(\) \{([\s\S]*?)\n  \}/);
        if (constructorMatch) {
            details.constructor = this.parseConstructor(constructorMatch[2], constructorMatch[1]);
        }

        // 提取方法 - 查找 prototype 方法
        const methodPattern = /([_\w]+)\.prototype\.(\w+) = function \(([^)]*)\) \{([\s\S]*?)\n  \};/g;
        let methodMatch;

        while ((methodMatch = methodPattern.exec(classContent)) !== null) {
            const methodName = methodMatch[2];
            const methodParams = methodMatch[3];
            const methodBody = methodMatch[4];

            details.methods.push({
                name: methodName,
                params: methodParams ? methodParams.split(',').map(p => p.trim()).filter(p => p) : [],
                body: this.cleanMethodBody(methodBody)
            });
        }

        return details;
    }

    // 解析构造函数
    parseConstructor(constructorBody, constructorVar) {
        const properties = [];

        // 查找属性初始化 - 支持 t.property 或 _ctor.property
        const propPattern = /(\w+)\.(\w+) = ([^;]+);/g;
        let match;

        while ((match = propPattern.exec(constructorBody)) !== null) {
            const varName = match[1];
            const propName = match[2];
            const propValue = match[3];

            // 检查是否是构造函数变量
            if (varName === constructorVar || varName === 't' || varName === '_ctor') {
                properties.push({
                    name: propName,
                    value: propValue
                });
            }
        }

        return { properties };
    }

    // 清理方法体
    cleanMethodBody(body) {
        return body
            .replace(/var e = this;/g, '')
            .replace(/var t = this;/g, '')
            .replace(/var o = this;/g, '')
            .replace(/var i = this;/g, '')
            .trim();
    }

    // 提取方法参数
    extractMethodParams(methodDef) {
        const match = methodDef.match(/function \(([^)]*)\)/);
        if (match && match[1].trim()) {
            return match[1].split(',').map(p => p.trim());
        }
        return [];
    }

    // 生成类代码
    generateClassCode(classInfo) {
        const baseClass = classInfo.baseClass || '$2Buff.Buff.BuffItem';
        let code = `@ccclass\nexport class ${classInfo.className} extends ${baseClass} {\n`;

        // 添加属性声明
        if (classInfo.constructor && classInfo.constructor.properties.length > 0) {
            classInfo.constructor.properties.forEach(prop => {
                code += `    ${prop.name}: any;\n`;
            });
            code += '\n';
        }

        // 添加构造函数
        if (classInfo.constructor && classInfo.constructor.properties.length > 0) {
            code += '    constructor() {\n';
            code += '        super();\n';
            classInfo.constructor.properties.forEach(prop => {
                code += `        this.${prop.name} = ${prop.value};\n`;
            });
            code += '    }\n\n';
        }

        // 添加方法
        classInfo.methods.forEach(method => {
            const params = method.params.length > 0 ? method.params.join(', ') : '';
            code += `    ${method.name}(${params}) {\n`;
            code += '        // TODO: 实现方法体\n';
            code += '    }\n\n';
        });

        code += '}\n';
        return code;
    }
}

// 使用示例
if (require.main === module) {
    const converter = new MultiClassConverter();
    
    const args = process.argv.slice(2);
    const inputFile = args[0] || './scripts/BuffList.js';
    const outputFile = args[1] || './output/BuffList_corrected.ts';
    
    console.log(`📁 输入文件: ${inputFile}`);
    console.log(`📁 输出文件: ${outputFile}\n`);
    
    converter.convertMultiClassFile(inputFile, outputFile);
}

module.exports = MultiClassConverter;
