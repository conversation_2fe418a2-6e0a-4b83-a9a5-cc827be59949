import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_Pop_DiffSelectGeneral")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M33_Pop_DiffSelectGeneral extends $2Pop.Pop {
    get mode() {
        return $2ModeChainsModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    setInfo() {
        this.lvCfg = this.param;
            const e = this.node.getChildByPath("bg/content/Layout");
            e.children[1].getChildByName("lock").setActive($2Manager.Manager.leveMgr.vo.curPassLv < 4);
            e.children[2].getChildByName("lock").setActive($2Manager.Manager.leveMgr.vo.curPassLv < 6);
    }

    onBtn(e: any, t: any) {
        const o = +t;
            $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Fight").selectDiff(o);
            this.close();
    }

}
