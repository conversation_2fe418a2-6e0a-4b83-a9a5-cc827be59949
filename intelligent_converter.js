const fs = require('fs');
const path = require('path');

class IntelligentJSToTSConverter {
    constructor() {
        this.importMap = new Map();
        this.classHierarchy = new Map(); // 存储类继承关系
        this.commonPatterns = this.initializePatterns();
    }

    // 初始化常见模式
    initializePatterns() {
        return {
            // 变量声明模式
            varDeclarations: [
                /var e = this;/g,
                /var t = this;/g,
                /var o = this;/g,
                /var i = this;/g,
                /var n = this;/g
            ],
            
            // 父类调用模式
            superCalls: [
                /(\w+)\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g,
                /this\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g
            ],
            
            // 变量引用模式
            varReferences: [
                { from: /\be\./g, to: 'this.' },
                { from: /\bt\./g, to: 'this.' },
                { from: /\bo\./g, to: 'this.' },
                { from: /\bi\./g, to: 'this.' },
                { from: /\bn\./g, to: 'this.' }
            ],
            
            // 常见JS到TS转换
            jsToTs: [
                { from: /cc__assign/g, to: 'Object.assign' },
                { from: /null !== (\w+) && \w+ \|\| /g, to: '$1 || ' },
                { from: /undefined === (\w+)/g, to: '$1 === undefined' },
                { from: /null === (\w+)/g, to: '$1 === null' }
            ]
        };
    }

    // 智能批量转换
    async intelligentBatchConvert(inputDir, outputDir) {
        console.log('🚀 开始智能批量转换...\n');
        
        // 第一阶段：扫描所有文件，建立类继承关系图
        console.log('📊 第一阶段：分析文件结构...');
        await this.analyzeFileStructure(inputDir);
        
        // 第二阶段：批量转换
        console.log('🔄 第二阶段：批量转换文件...');
        const result = await this.batchConvertWithContext(inputDir, outputDir);
        
        // 第三阶段：后处理优化
        console.log('✨ 第三阶段：后处理优化...');
        await this.postProcessOptimization(outputDir);
        
        return result;
    }

    // 分析文件结构
    async analyzeFileStructure(inputDir) {
        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.js'));
        
        for (const file of files) {
            const filePath = path.join(inputDir, file);
            const content = fs.readFileSync(filePath, 'utf8');
            
            // 分析类定义和继承关系
            this.analyzeClassHierarchy(content, file);
        }
        
        console.log(`   📋 分析了 ${files.length} 个文件`);
        console.log(`   🔗 发现 ${this.classHierarchy.size} 个类继承关系\n`);
    }

    // 分析类继承关系
    analyzeClassHierarchy(content, fileName) {
        // 单类模式
        const singleClassMatch = content.match(/var (def_|exp_)(\w+) = function \(e\) \{[\s\S]*?\}(\(\$\d+\w+(?:\.\w+)*\));/);
        if (singleClassMatch) {
            const className = singleClassMatch[2];
            const baseClass = singleClassMatch[3].replace(/[()]/g, '');
            this.classHierarchy.set(className, { baseClass, fileName, type: 'single' });
        }
        
        // 多类模式
        const multiClassMatches = content.matchAll(/var (exp_\w+) = function \(e\) \{[\s\S]*?\}(\([^)]+\));/g);
        for (const match of multiClassMatches) {
            const fullVarName = match[1];
            const className = fullVarName.replace('exp_', '');
            const baseClassCall = match[2];
            
            let baseClass;
            if (baseClassCall.includes('$')) {
                baseClass = baseClassCall.replace(/[()]/g, '');
            } else {
                baseClass = baseClassCall.replace(/[()exp_]/g, '');
            }
            
            this.classHierarchy.set(className, { baseClass, fileName, type: 'multi' });
        }
    }

    // 带上下文的批量转换
    async batchConvertWithContext(inputDir, outputDir) {
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.js'));
        let successCount = 0;
        const failedFiles = [];

        for (const file of files) {
            const inputPath = path.join(inputDir, file);
            const outputPath = path.join(outputDir, file.replace('.js', '.ts'));
            
            try {
                const success = await this.convertFileWithContext(inputPath, outputPath);
                if (success) {
                    successCount++;
                    console.log(`✅ ${file}`);
                } else {
                    failedFiles.push(file);
                    console.log(`❌ ${file}`);
                }
            } catch (error) {
                failedFiles.push(file);
                console.log(`❌ ${file} - ${error.message}`);
            }
        }

        console.log(`\n🎉 转换完成! 成功: ${successCount}/${files.length}`);
        if (failedFiles.length > 0) {
            console.log(`⚠️  失败文件: ${failedFiles.join(', ')}`);
        }

        return { successCount, failedFiles };
    }

    // 带上下文的文件转换
    async convertFileWithContext(inputPath, outputPath) {
        const jsContent = fs.readFileSync(inputPath, 'utf8');
        const fileName = path.basename(inputPath, '.js');
        
        // 检测文件类型
        const isMultiClass = this.detectMultiClass(jsContent);
        
        let tsContent;
        if (isMultiClass) {
            tsContent = this.convertMultiClassWithContext(jsContent, fileName);
        } else {
            tsContent = this.convertSingleClassWithContext(jsContent, fileName);
        }
        
        if (tsContent) {
            fs.writeFileSync(outputPath, tsContent, 'utf8');
            return true;
        }
        
        return false;
    }

    // 检测多类文件
    detectMultiClass(content) {
        const expClassMatches = content.match(/var exp_\w+ = function \(e\) \{/g);
        return expClassMatches && expClassMatches.length > 1;
    }

    // 智能转换多类文件
    convertMultiClassWithContext(content, fileName) {
        // 使用之前的多类转换逻辑，但加入上下文信息
        const imports = this.extractImports(content);
        const classes = this.extractMultipleClassesWithContext(content);
        
        if (!classes || classes.length === 0) {
            return null;
        }

        let tsCode = '';
        
        // 导入语句
        if (imports.length > 0) {
            tsCode += imports.join('\n') + '\n\n';
        }
        
        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';
        
        // 生成类，使用上下文信息优化
        classes.forEach((classInfo, index) => {
            tsCode += this.generateOptimizedClassCode(classInfo);
            if (index < classes.length - 1) {
                tsCode += '\n';
            }
        });

        return tsCode;
    }

    // 智能转换单类文件
    convertSingleClassWithContext(content, fileName) {
        // 使用现有的单类转换逻辑
        const imports = this.extractImports(content);
        const classInfo = this.extractClassInfoWithContext(content, fileName);
        
        if (!classInfo) {
            return null;
        }

        // 生成优化的单类代码
        return this.generateSingleClassCode(imports, classInfo, content);
    }

    // 后处理优化
    async postProcessOptimization(outputDir) {
        const files = fs.readdirSync(outputDir).filter(file => file.endsWith('.ts'));
        
        for (const file of files) {
            const filePath = path.join(outputDir, file);
            await this.optimizeFile(filePath);
        }
        
        console.log(`   🔧 优化了 ${files.length} 个文件\n`);
    }

    // 优化单个文件
    async optimizeFile(filePath) {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 应用所有优化模式
        content = this.applyOptimizationPatterns(content);
        
        fs.writeFileSync(filePath, content, 'utf8');
    }

    // 应用优化模式
    applyOptimizationPatterns(content) {
        // 移除变量声明
        this.commonPatterns.varDeclarations.forEach(pattern => {
            content = content.replace(pattern, '');
        });
        
        // 修正父类调用
        content = content.replace(/(\w+)\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$2($3)');
        content = content.replace(/this\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)');
        
        // 变量引用转换
        this.commonPatterns.varReferences.forEach(({ from, to }) => {
            content = content.replace(from, to);
        });
        
        // JS到TS转换
        this.commonPatterns.jsToTs.forEach(({ from, to }) => {
            content = content.replace(from, to);
        });
        
        // 清理多余空行
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
        
        return content;
    }

    // 其他必要的方法（从之前的转换器复制）
    extractImports(content) {
        const imports = [];
        const regex = /var (\$\d*\$?\d*\w+) = require\("(\w+)"\);/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            const moduleName = match[1];
            const fileName = match[2];
            imports.push(`import ${moduleName} from "./${fileName}";`);
        }

        return imports;
    }

    // 提取多类（带上下文）
    extractMultipleClassesWithContext(content) {
        const classes = [];
        const lines = content.split('\n');
        let currentClass = null;
        let classContent = [];
        let braceCount = 0;
        let inClass = false;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            const classMatch = line.match(/var (exp_\w+) = function \(e\) \{/);
            if (classMatch) {
                if (currentClass) {
                    this.processClassWithContext(currentClass, classContent, classes);
                }

                currentClass = {
                    fullVarName: classMatch[1],
                    className: classMatch[1].replace('exp_', ''),
                    startLine: i
                };
                classContent = [line];
                braceCount = 1;
                inClass = true;
                continue;
            }

            if (inClass) {
                classContent.push(line);
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                if (braceCount === 0 && line.includes('}(')) {
                    // 使用上下文信息确定基类
                    currentClass.baseClass = this.determineBaseClass(line, currentClass.className);

                    this.processClassWithContext(currentClass, classContent, classes);
                    currentClass = null;
                    classContent = [];
                    inClass = false;
                }
            }
        }

        return classes;
    }

    // 智能确定基类
    determineBaseClass(line, className) {
        // 首先尝试从类继承关系图中获取
        if (this.classHierarchy.has(className)) {
            return this.classHierarchy.get(className).baseClass;
        }

        // 然后尝试从当前行解析
        let baseMatch = line.match(/\}(\(\$\d+\w+(?:\.\w+)*\));/);
        if (baseMatch) {
            return baseMatch[1].replace(/[()]/g, '');
        }

        baseMatch = line.match(/\}\((exp_\w+)\);/);
        if (baseMatch) {
            return baseMatch[1].replace('exp_', '');
        }

        return '$2Buff.Buff.BuffItem'; // 默认基类
    }

    // 处理类（带上下文）
    processClassWithContext(classInfo, classContent, classes) {
        const fullContent = classContent.join('\n');
        const classDetails = this.extractClassDetailsWithContext(fullContent, classInfo);

        classes.push({
            ...classInfo,
            classContent: fullContent,
            ...classDetails
        });
    }

    // 提取类详情（带上下文）
    extractClassDetailsWithContext(classContent, classInfo) {
        const details = {
            constructor: null,
            methods: [],
            properties: [],
            decorators: []
        };

        // 装饰器
        if (classContent.includes('ccp_ccclass')) {
            details.decorators.push('@ccclass');
        }

        // 构造函数
        const constructorMatch = classContent.match(/function ([_\w]+)\(\) \{([\s\S]*?)\n  \}/);
        if (constructorMatch) {
            details.constructor = this.parseConstructorWithContext(constructorMatch[2], constructorMatch[1]);
        }

        // 方法
        const methodPattern = /([_\w]+)\.prototype\.(\w+) = function \(([^)]*)\) \{([\s\S]*?)\n  \};/g;
        let methodMatch;

        while ((methodMatch = methodPattern.exec(classContent)) !== null) {
            details.methods.push({
                name: methodMatch[2],
                params: methodMatch[3] ? methodMatch[3].split(',').map(p => p.trim()).filter(p => p) : [],
                body: this.cleanMethodBodyWithContext(methodMatch[4], classInfo.className)
            });
        }

        return details;
    }

    // 解析构造函数（带上下文）
    parseConstructorWithContext(constructorBody, constructorVar) {
        const properties = [];
        const propPattern = /(\w+)\.(\w+) = ([^;]+);/g;
        let match;

        while ((match = propPattern.exec(constructorBody)) !== null) {
            if (match[1] === constructorVar || match[1] === 't' || match[1] === '_ctor') {
                properties.push({
                    name: match[2],
                    value: match[3]
                });
            }
        }

        return { properties };
    }

    // 清理方法体（带上下文）
    cleanMethodBodyWithContext(body, className) {
        if (!body || !body.trim()) return '';

        let cleanBody = body;

        // 应用所有清理模式
        cleanBody = this.applyOptimizationPatterns(cleanBody);

        // 特定于类的优化
        if (className.includes('Buff')) {
            // Buff类特定的优化
            cleanBody = cleanBody.replace(/this\.mgr\.clearBuff\(this\)/g, 'this.mgr.clearBuff(this)');
        }

        return cleanBody.trim();
    }

    // 生成优化的类代码
    generateOptimizedClassCode(classInfo) {
        const baseClass = classInfo.baseClass || '$2Buff.Buff.BuffItem';
        let code = `@ccclass\nexport class ${classInfo.className} extends ${baseClass} {\n`;

        // 属性声明
        if (classInfo.constructor && classInfo.constructor.properties.length > 0) {
            classInfo.constructor.properties.forEach(prop => {
                code += `    ${prop.name}: any;\n`;
            });
            code += '\n';
        }

        // 构造函数
        if (classInfo.constructor && classInfo.constructor.properties.length > 0) {
            code += '    constructor() {\n';
            code += '        super();\n';
            classInfo.constructor.properties.forEach(prop => {
                code += `        this.${prop.name} = ${prop.value};\n`;
            });
            code += '    }\n\n';
        }

        // 方法
        classInfo.methods.forEach(method => {
            const params = method.params.join(', ');
            code += `    ${method.name}(${params}) {\n`;

            if (method.body.trim()) {
                const lines = method.body.split('\n');
                lines.forEach(line => {
                    if (line.trim()) {
                        code += `        ${line.trim()}\n`;
                    }
                });
            }

            code += '    }\n\n';
        });

        code += '}\n';
        return code;
    }

    // 提取类信息（带上下文）
    extractClassInfoWithContext(content, fileName) {
        // 使用现有逻辑，但加入上下文
        let classMatch = content.match(/var def_(\w+) = function \(e\) \{/);
        if (!classMatch) {
            classMatch = content.match(/var exp_(\w+) = function \(e\) \{/);
        }

        if (!classMatch) return null;

        const className = classMatch[1];

        // 从类继承关系图获取基类信息
        let baseClass = 'cc.Component';
        if (this.classHierarchy.has(className)) {
            baseClass = this.classHierarchy.get(className).baseClass;
        }

        return { className, baseClass };
    }

    // 生成单类代码
    generateSingleClassCode(imports, classInfo, content) {
        let tsCode = '';

        if (imports.length > 0) {
            tsCode += imports.join('\n') + '\n\n';
        }

        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';

        // 提取装饰器
        const decorators = [];
        if (content.includes('ccp_ccclass')) {
            decorators.push('@ccclass');
        }

        // 生成类
        tsCode += decorators.join('\n') + '\n';
        tsCode += `export default class ${classInfo.className} extends ${classInfo.baseClass} {\n`;
        tsCode += '    // TODO: 添加属性和方法\n';
        tsCode += '}\n';

        return tsCode;
    }
}

// 使用示例
if (require.main === module) {
    const converter = new IntelligentJSToTSConverter();
    
    const args = process.argv.slice(2);
    const inputDir = args[0] || './scripts';
    const outputDir = args[1] || './output_intelligent';
    
    console.log(`📁 输入目录: ${inputDir}`);
    console.log(`📁 输出目录: ${outputDir}\n`);
    
    converter.intelligentBatchConvert(inputDir, outputDir)
        .then(result => {
            console.log('🎉 智能转换完成!');
            console.log(`✅ 成功: ${result.successCount} 个文件`);
            if (result.failedFiles.length > 0) {
                console.log(`❌ 失败: ${result.failedFiles.length} 个文件`);
            }
        })
        .catch(error => {
            console.error('❌ 转换失败:', error);
        });
}

module.exports = IntelligentJSToTSConverter;
