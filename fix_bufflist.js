const fs = require('fs');

// 手动修正 BuffList.ts 文件中的继承关系
function fixBuffListInheritance() {
    const filePath = './output/BuffList_corrected.ts';
    
    if (!fs.existsSync(filePath)) {
        console.error('文件不存在:', filePath);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 定义正确的继承关系映射
    const inheritanceMap = {
        'Buff_OnTime': 'Buff_Excute',
        'Buff_ContinuousRecovery': 'Buff_Excute',
        'Buff_HPLinkOnce': 'Buff_HPLink',
        'Buff_VicinityHurt': 'Buff_Excute',
        'Buff_Halo': 'Buff_Excute',
        'Buff_Hurt': 'Buff_Excute',
        'Buff_AtkFocus': 'Buff_OnSpawnHurt',
        'Buff_AdrenalTechnology': 'Buff_OnSpawnHurt',
        'Buff_ReboundDam': 'Buff_OnBehit',
        'Buff_OnKillLayout': 'Buff_OnKill',
        'Buff_OnLifeVal': 'Buff_OnBehit',
        'Buff_OnSpawnHurtAddArmor': 'Buff_OnSpawnHurt',
        'Buff_OnBehitAddArmor': 'Buff_OnBehit',
        'Buff_RestoreArmor': 'Buff_Excute',
        'Buff_Vampire': 'Buff_OnSpawnHurt'
    };
    
    // 修正每个类的继承关系
    Object.entries(inheritanceMap).forEach(([className, baseClass]) => {
        const pattern = new RegExp(`export class ${className} extends undefined`, 'g');
        content = content.replace(pattern, `export class ${className} extends ${baseClass}`);
    });
    
    // 写回文件
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ BuffList.ts 继承关系修正完成');
    
    // 显示修正的类
    console.log('\n📝 修正的继承关系:');
    Object.entries(inheritanceMap).forEach(([className, baseClass]) => {
        console.log(`   - ${className} extends ${baseClass}`);
    });
}

// 运行修正
if (require.main === module) {
    fixBuffListInheritance();
}

module.exports = { fixBuffListInheritance };
