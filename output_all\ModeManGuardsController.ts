import $2CallID from "./CallID";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2ListenID from "./ListenID";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2NodePool from "./NodePool";
import $2ModeManGuardsModel from "./ModeManGuardsModel";

const { ccclass, property, menu } = cc._decorator;

export default class ModeManGuardsController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}