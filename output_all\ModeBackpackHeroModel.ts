import $2MVC from "./MVC";
import $2KnapsackVo from "./KnapsackVo";
import $2Game from "./Game";
import $2Cfg from "./Cfg";
import $2GameUtil from "./GameUtil";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameSeting from "./GameSeting";
import $2RedPointTree from "./RedPointTree";
import $2CallID from "./CallID";
import $2RecordVo from "./RecordVo";
import $2GameatrCfg from "./GameatrCfg";
import $2RBadgeModel from "./RBadgeModel";

const { ccclass, property, menu } = cc._decorator;

export default class ModeBackpackHeroModel extends $2RecordVo.RecordVo.Data {
    // TODO: 添加属性和方法
}