Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2LocalStorage = require("LocalStorage");
var n = function () {
  function e() {
    this._showParams = {};
    this._gameParams = {
      app_name: "",
      channel: "",
      version: ""
    };
    this._userParams = {
      openid: ""
    };
    this._openidKey = "sdk_openid";
    this._userParams.openid = $2LocalStorage.default.getItem(this._openidKey);
  }
  e.prototype.setShowParams = function (e) {
    return this._showParams = e;
  };
  e.prototype.getShowParams = function (e) {
    undefined === e && (e = "");
    if ("" === e) {
      return this._showParams;
    } else {
      if (undefined === this._showParams[e]) {
        return "";
      } else {
        return this._showParams[e];
      }
    }
  };
  e.prototype.setGameParams = function (e) {
    return this._gameParams = e;
  };
  e.prototype.getGameParams = function (e) {
    undefined === e && (e = "");
    if ("" === e) {
      return this._gameParams;
    } else {
      if (undefined === this._gameParams[e]) {
        return "";
      } else {
        return this._gameParams[e];
      }
    }
  };
  e.prototype.setUserParams = function (e) {
    $2LocalStorage.default.setItem(this._openidKey, undefined === e.openid ? "" : e.openid);
    return this._userParams = e;
  };
  e.prototype.getUserParams = function (e) {
    undefined === e && (e = "");
    if ("" === e) {
      return this._userParams;
    } else {
      if (undefined === this._userParams[e]) {
        return "";
      } else {
        return this._userParams[e];
      }
    }
  };
  return e;
}();
exports.default = new n();