import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_ShopBoxInfo")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
@$2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Popup)
export default class M20_Pop_ShopBoxInfo extends $2Pop.Pop {
    @property(cc.Node)
    normalcontent: cc.Node = null;

    @property(cc.Node)
    highcontent: cc.Node = null;

    constructor() {
        super();
        this.curlv = 1;
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    setInfo() {
        let e: any;
            let t: any;
            let o: any;
            let i: any;
            this.curlv = this.mode.fightinfopack.getVal("boxlv");
            this.curlv > $2Cfg.Cfg.BoxLevelExp.getArray().length && (this.curlv = $2Cfg.Cfg.BoxLevelExp.getArray().length);
            this.labelArr[0].string = "宝箱奖励";
            this.nodeArr[0].active = !(null === (t = null === (e = this._openArgs) || e === undefined ? undefined : e.param) || t === undefined ? undefined : t.content);
            if (null === (i = null === (o = this._openArgs) || o === undefined ? undefined : o.param) || i === undefined ? undefined : i.content) {
              const n = this._openArgs.param.content;
              if (this.curlv >= $2Cfg.Cfg.BoxLevelExp.getArray().length) {
                this.labelArr[0].string = cc.js.formatStr("宝箱升级 LV.%d -> MAX", n.from);
              } else {
                this.labelArr[0].string = cc.js.formatStr("宝箱升级 LV.%d -> LV.%d", n.from, this.curlv + 1);
              }
            }
            this.pageChange(null, 0);
    }

    pageChange(e: any, t: any) {
        const o = this;
            const i = this.curlv + Number(t);
            const n = $2Cfg.Cfg.BagShopItem.find({
              type: $2CurrencyConfigCfg.CurrencyConfigDefine.Box,
              id: 1e4 + i
            });
            const r = $2Cfg.Cfg.BagShopItem.find({
              type: $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box,
              id: 10100 + i
            });
            if (!(!n || !r || i > $2Cfg.Cfg.BoxLevelExp.getArray().length)) {
              this.curlv += Number(t);
              this.labelArr[1].string = "LV." + this.curlv;
              this.normalcontent.children.forEach(function (e) {
                return e.active = false;
              });
              this.highcontent.children.forEach(function (e) {
                return e.active = false;
              });
              n.boxReward.forEach(function (e, t) {
                const i = e[1];
                const n = e[0];
                e[2];
                const r = o.normalcontent.children[t] || cc.instantiate(o.normalcontent.children[0]);
                r.setAttribute({
                  parent: o.normalcontent
                });
                r.active = true;
                let c: any;
                const l = $2Cfg.Cfg.CurrencyConfig.find({
                  id: n
                });
                c = o.mode.fragments.includes(n) ? $2GameSeting.GameSeting.getRarity(o.mode.buffmap[l.id]).framgimg : l.icon;
                $2Manager.Manager.loader.loadSpriteToSprit(c, r.getChildByName("icon").getComponent(cc.Sprite), o.node);
                r.getChildByName("num").getComponent(cc.Label).string = i;
              });
              r.boxReward.forEach(function (e, t) {
                const i = e[1];
                const n = e[0];
                e[2];
                const r = o.highcontent.children[t] || cc.instantiate(o.highcontent.children[0]);
                r.setAttribute({
                  parent: o.highcontent
                });
                r.active = true;
                let c: any;
                const l = $2Cfg.Cfg.CurrencyConfig.find({
                  id: n
                });
                c = o.mode.fragments.includes(n) ? $2GameSeting.GameSeting.getRarity(o.mode.buffmap[l.id]).framgimg : l.icon;
                $2Manager.Manager.loader.loadSpriteToSprit(c, r.getChildByName("icon").getComponent(cc.Sprite), o.node);
                r.getChildByName("num").getComponent(cc.Label).string = i;
              });
            }
    }

    onClickFrame() {
        this.close();
    }

}
