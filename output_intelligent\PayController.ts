import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2Manager from "./Manager";
import $2AlertManager from "./AlertManager";
import $2PayModel from "./PayModel";

const { ccclass, property, menu } = cc._decorator;

export default class PayController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}
