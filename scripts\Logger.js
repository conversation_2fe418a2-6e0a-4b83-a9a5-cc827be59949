var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isLegalLogLevel = exports.LOG_LEVEL = undefined;
var $2Manager = require("Manager");
function r(e, t) {
  var o = {
    "M+": e.getMonth() + 1,
    "d+": e.getDate(),
    "h+": e.getHours() % 12 == 0 ? 12 : e.getHours() % 12,
    "H+": e.getHours(),
    "m+": e.getMinutes(),
    "s+": e.getSeconds(),
    "q+": Math.floor((e.getMonth() + 3) / 3),
    S: e.getMilliseconds()
  };
  /(y+)/.test(t) && (t = t.replace(RegExp.$1, (e.getFullYear() + "").substr(4 - RegExp.$1.length)));
  /(E+)/.test(t) && (t = t.replace(RegExp.$1, (RegExp.$1.length > 1 ? RegExp.$1.length > 2 ? "星期" : "周" : "") + "日一二三四五六".charAt(e.getDay())));
  for (var i in o) {
    new RegExp("(" + i + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? o[i] : ("00" + o[i]).substr(("" + o[i]).length)));
  }
  return t;
}
exports.LOG_LEVEL = {
  VERBOSE: "verbose",
  INFO: "info",
  WARNING: "warning",
  ERROR: "error",
  NONE: "none"
};
exports.isLegalLogLevel = function (e) {
  return [exports.LOG_LEVEL.VERBOSE, exports.LOG_LEVEL.INFO, exports.LOG_LEVEL.WARNING, exports.LOG_LEVEL.ERROR, exports.LOG_LEVEL.NONE].includes(e);
};
var def_Logger$$1 = function () {
  function _ctor(t, o) {
    undefined === t && (t = "none");
    undefined === o && (o = "LOG");
    this.level = t;
    this.prefix = o;
    this.id = _ctor.id;
  }
  _ctor.isLegalLogLevel = function (e) {
    return [exports.LOG_LEVEL.VERBOSE, exports.LOG_LEVEL.INFO, exports.LOG_LEVEL.WARNING, exports.LOG_LEVEL.ERROR, exports.LOG_LEVEL.NONE].includes(e);
  };
  _ctor.prototype.getPrintPrefix = function (e) {
    this.id++;
    return "[wonder-js-sdk] " + r(new Date(), "yyyy-MM-ddThh:mm:ss.SZ") + " [" + e.toLocaleUpperCase() + "][" + this.prefix + "#" + this.id + "]:";
  };
  _ctor.prototype.setLogLevel = function (e) {
    return this.level = e;
  };
  _ctor.prototype.getLogLevel = function () {
    return this.level;
  };
  _ctor.prototype.info = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
    if ($2Manager.Manager.vo.switchVo.isTaLog) {
      var o = ["info"];
      o.includes(this.level) && console.log.apply(console, cc__spreadArrays([this.getPrintPrefix("info")], e));
    }
  };
  _ctor.prototype.warn = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
    var o = ["info", "warn"];
    o.includes(this.level) && console.warn.apply(console, cc__spreadArrays([this.getPrintPrefix("warn")], e));
  };
  _ctor.prototype.error = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
    var o = ["info", "warn", "error", "verbose"];
    o.includes(this.level) && console.error.apply(console, cc__spreadArrays([this.getPrintPrefix("error")], e));
  };
  _ctor.prototype.verbose = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
    var o = ["info", "warn", "error", "verbose"];
    o.includes(this.level) && console.error.apply(console, cc__spreadArrays([this.getPrintPrefix("verbose")], e));
  };
  _ctor.id = 0;
  _ctor.LOG_LEVEL = {
    VERBOSE: "verbose",
    INFO: "info",
    WARNING: "warning",
    ERROR: "error",
    NONE: "none"
  };
  return _ctor;
}();
exports.default = def_Logger$$1;