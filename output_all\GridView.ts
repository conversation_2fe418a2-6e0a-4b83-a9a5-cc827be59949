import $2PoolArray from "./PoolArray";
import $2GridViewCell from "./GridViewCell";
import $2TwoDLayoutObject from "./TwoDLayoutObject";
import $2TwoDHorizontalLayoutObject from "./TwoDHorizontalLayoutObject";
import $2MathSection from "./MathSection";
import $2MathUtils from "./MathUtils";
import $2AutoScaleComponent from "./AutoScaleComponent";
import $2GridViewFreshWork from "./GridViewFreshWork";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class GridView extends cc.Component {
    // TODO: 添加属性和方法
}