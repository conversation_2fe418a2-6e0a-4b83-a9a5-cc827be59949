import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2Listen<PERSON> from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20EquipitemBlock from "./M20EquipitemBlock";
import $2M20EquipitemList from "./M20EquipitemList";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_PrePare_Equip extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
