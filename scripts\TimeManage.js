Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2function = require("function");
var $2LocalStorage = require("LocalStorage");
var r = function () {
  function e() {
    this._startClientTime = "sdk_s_c_t";
    this._endClientTime = "sdk_e_c_t";
    this._firstReportServerTime = "sdk_s_b_t";
    this._firstReportClientTime = "sdk_c_b_t";
    this.lockGetFirstServerTime = "sdk_lock_get_fst";
  }
  e.prototype.setStartClientTime = function () {
    $2LocalStorage.default.setItem(this._startClientTime, $2function.getClientTime());
  };
  e.prototype.getStartClientTime = function () {
    var e = $2LocalStorage.default.getItem(this._startClientTime);
    if (e > 0) {
      return e;
    } else {
      return 0;
    }
  };
  e.prototype.removeStartClientTime = function () {
    $2LocalStorage.default.removeItem(this._startClientTime);
  };
  e.prototype.setEndClientTime = function () {
    $2LocalStorage.default.setItem(this._endClientTime, $2function.getClientTime());
  };
  e.prototype.getEndClientTime = function () {
    var e = $2LocalStorage.default.getItem(this._endClientTime);
    if (e > 0) {
      return e;
    } else {
      return 0;
    }
  };
  e.prototype.removeEndClientTime = function () {
    $2LocalStorage.default.removeItem(this._endClientTime);
  };
  e.prototype.getOnlineTime = function () {
    var e = this.getStartClientTime();
    var t = this.getEndClientTime();
    if (e > 0 && t > 0) {
      return t - e;
    } else {
      return 0;
    }
  };
  e.prototype.setFirstReportServerTime = function () {
    var e = this;
    if (!$2LocalStorage.default.getItem(this.lockGetFirstServerTime)) {
      $2LocalStorage.default.setItem(this.lockGetFirstServerTime, $2function.getClientTime());
      $2function.getServerTime(function (t) {
        try {
          $2LocalStorage.default.setItem(e._firstReportServerTime, t.data.data.time);
          $2LocalStorage.default.removeItem(e.lockGetFirstServerTime);
        } catch (o) {}
      });
    }
  };
  e.prototype.getFirstReportServerTime = function () {
    return $2LocalStorage.default.getItem(this._firstReportServerTime);
  };
  e.prototype.removeFirstReportServerTime = function () {
    return $2LocalStorage.default.removeItem(this._firstReportServerTime);
  };
  e.prototype.setFirstReportClientTime = function () {
    $2LocalStorage.default.setItem(this._firstReportClientTime, $2function.getClientTime());
  };
  e.prototype.getFirstReportClientTime = function () {
    return $2LocalStorage.default.getItem(this._firstReportClientTime);
  };
  e.prototype.removeFirstReportClientTime = function () {
    return $2LocalStorage.default.removeItem(this._firstReportClientTime);
  };
  return e;
}();
exports.default = new r();