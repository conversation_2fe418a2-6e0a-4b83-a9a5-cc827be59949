import $2GameSeting from "./GameSeting";
import $2L<PERSON>enID from "./ListenID";
import $2AutoFollow from "./AutoFollow";
import $2GameatrCfg from "./GameatrCfg";
import $2SoundCfg from "./SoundCfg";
import $2Notifier from "./Notifier";
import $2StateMachine from "./StateMachine";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2Intersection from "./Intersection";
import $2MChains from "./MChains";
import $2Game from "./Game";
import $2SkillModule from "./SkillModule";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export class Skill_Default extends $2SkillModule.BaseSkill {
    excute(t) {
        super.excute(t);
    }

}

@ccclass
export class Skill_AtkDefault extends $2SkillModule.BaseSkill {
    game: any;
    getBulletVo: any;

    excute(t) {
        super.excute(t);
        t.forEach(function (e, t) {
        this.owner.delayByGame(function () {
        this.fire({
        pos: e.position,
        shootDir: this.launchPoint.sub(b).normalizeSelf()
    });
    }, this.cutVo.barrageCd * t);
    });
    }

    fire(e) {
        var t = this.getBulletVo({
        startPos: e.pos,
        shootDir: e.shootDir
    });
        this.game.spawnBullet(t.bulletPath, t);
    }

}

@ccclass
export class Skill_NormalChop extends $2SkillModule.BaseSkill {
    _owner: any;
    audioID: any;
    cutVo: any;
    isStartSkill: any;
    owner: any;
    radioScattering: any;
    setAm: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_zj;
    }

    excute() {
        super.excute();
        this.radioScattering().forEach(function (e, o) {
        this.owner.delayByGame(function () {
        (b = this._owner.position.add(e.mul(100))).y += this._owner.node.height / 2;
        var o = this.getBulletVo({
        lifeTime: .2,
        speed: 100,
        startPos: b,
        shootDir: e
    });
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o);
    }, this.cutVo.barrageCd * o);
    });
        this.isStartSkill && this.setAm();
    }

    setAm() {

        var o = null === (t = null === (e = this._owner) || e === undefined ? undefined : e.mySkeleton) || t === undefined ? undefined : t.setAnimation(1, this._owner.curStateTag == $2StateMachine.State.Type.IDLE ? "attack01" : "attack02", false);
        o.timeScale = 1 / this.cutVo.cd * 2;
        this.owner.delayByGame(function () {
        o.alpha = .5;
    }, o.animationEnd * o.timeScale * .5);
    }

}

@ccclass
export class Skill_MagicBall extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    isStartSkill: any;
    setAm: any;

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        var i = e.position.clone();
        this.owner.delayByGame(function () {
        b = this.launchPoint;
        var e = this.getBulletVo({
        lifeTime: 4,
        speed: this.cutVo.barrangeSpeed,
        startPos: b
    });
        e.shootDir = i.sub(b).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(e.bulletPath, e, {
        opacity: 0
    }).then(function (e) {
        $2Game.Game.tween(e.node).set({
        opacity: 0
    }).to(.1, {
        opacity: 255
    }).start();
    });
    }, this.cutVo.barrageCd * t);
    });
        this.isStartSkill && this.setAm();
    }

    setAm() {

        var o = null === (t = null === (e = this._owner) || e === undefined ? undefined : e.mySkeleton) || t === undefined ? undefined : t.setAnimation(1, this._owner.curStateTag == $2StateMachine.State.Type.IDLE ? "attack01" : "attack02", false);
        if (o) {
        o.timeScale = 1 / this.cutVo.cd * 2;
        o.alpha = 1;
        $2Game.Game.tween(o).to(o.animationEnd * o.timeScale * 2, {
        alpha: 0
    }).start();
    }
    }

}

@ccclass
export class Skill_Multishot extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    dis: any;
    game: any;

    excute(t) {
        super.excute(t);
        t.forEach(function (e, t) {
        var i = e.position.clone();
        this.owner.delayByGame(function () {
        b.set(this.launchPoint);
        var e = this.getBulletVo({
        startPos: b,
        targetPos: i,
        shootDir: i.sub(b).normalizeSelf()
    });
        this.game.spawnBullet(e.bulletPath, e);
    }, this.cutVo.barrageCd * t);
    });
    }

    fire(e) {
        this.game.LatticeElementMap.seekByPos({
        pos: e.pos,
        radius: this.dis,
        targetCamp: [this._owner.atkCamp],
        ignoreID: e.ignoreID
    }).slice(0, this.cutVo.barrageNum).forEach(function (o) {
        var i = this.getBulletVo({
        startPos: e.pos,
        ignore: e.ignoreID
    });
        i.shootDir = o.position.sub(e.pos).normalizeSelf();
        this.game.spawnBullet(i.bulletPath, i);
    });
    }

}

@ccclass
export class Skill_FireOffset extends $2SkillModule.BaseSkill {
    game: any;

    excute(t) {
        super.excute(t);
        var i = this.game.scenceSize;
        t.forEach(function (e, t) {
        var n = e.position.clone();
        this.owner.delayByGame(function () {
        b.set(this.launchPoint);
        var e = this.getBulletVo({
        startPos: b.add(cc.v2($2Game.Game.random(i[0], i[1]), 0)),
        shootDir: n.sub(b).normalizeSelf()
    });
        this.game.spawnBullet(e.bulletPath, e).then(function (e) {
        126e3 == this.id && $2Game.Game.tween(e.vo.shootDir).by($2Game.Game.random(5, 10) / 10, {
        x: $2Game.Game.random(-3, 3) / 10
    }).by($2Game.Game.random(5, 10) / 10, {
        x: $2Game.Game.random(-3, 3) / 10
    }).union().repeat(20).start();
    });
    }, this.cutVo.barrageCd * t);
    });
    }

}

@ccclass
export class Skill_ParallelFire extends $2SkillModule.BaseSkill {
    cutVo: any;
    game: any;
    getBulletVo: any;
    lotId: any;

    constructor() {
        super();
        this.lotId = 1;
    }

    excute(t) {
        super.excute(t);
        var i = this.cutVo.offSet * (this.cutVo.barrageNum - 1);
        t.forEach(function (e, t) {
        var n = e.position.sub(this.launchPoint).normalizeSelf();
        this.owner.delayByGame(function () {
        b.set(this.launchPoint);
        this.fire({
        pos: b.add(cc.v2(-i / 2 + this.cutVo.offSet * t, 0)),
        shootDir: n
    });
    }, this.cutVo.barrageCd * t);
    });
        this.lotId++;
    }

    fire(e) {
        var t = this.getBulletVo({
        startPos: e.pos,
        shootDir: e.shootDir
    });
        t.lotId = this.lotId;
        this.game.spawnBullet(t.bulletPath, t);
    }

}

@ccclass
export class Skill_Sniper extends $2SkillModule.BaseSkill {
    _owner: any;
    audioID: any;
    cutVo: any;
    dis: any;
    excuteEnd: any;
    getBulletVo: any;
    isReady: any;
    launchPoint: any;
    line: any;
    tempTarget: any;
    tickChange: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_dcj;
        this.tempTarget = [];
        this.line = null;
    }

    checkTarget() {
        this.line && (this.line.active = false);
        if (!(this.tempTarget.length > 0) || this.tempTarget[0].isValid) {
        if (this.tempTarget.length) {
        return this.excute(this.tempTarget[0]);
    } else {
        return void this.excuteEnd();
    }
    }
        this.excuteEnd();
    }

    load() {
        super.load();
        $2Manager.Manager.loader.loadPrefab("entity/fight/Bullet/sniperLine").then(function (e) {
        if (e) {
        this.line = e;
        this.line.active = false;
        this.line.setParent(this._owner.node);
        this.line.setPosition(30, 50);
    }
    });
        $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Tower_FightRoundTickStart, this.tickChange, this);
    }

    tickChange(e) {
        if (this.line) {
        this.line.active = !e;
        e && (this.line.height = 0);
    }
    }

    excuteEnd() {
        super.excuteEnd();
        this.tempTarget = [];
    }

    onUpdate(t) {
        super.onUpdate(t);
        if (!this.isReady || 0 == this.tempTarget.length) {
        this.tempTarget.length > 0 && this.tempTarget[0].isDead && (this.tempTarget = []);
        if (0 == this.tempTarget.length) {
        this.line && (this.line.active = true);
        this.tempTarget = $2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis
    });
    } else if (this.line) {
        var o = cc.Vec2.squaredDistance(this._owner.position, this.tempTarget[0].position);
        this.line.height = 4 * Math.sqrt(o);
        var i = this._owner.position.sub(this.tempTarget[0].position);
        this.line.angle = $2GameUtil.GameUtil.GetAngle(i.normalizeSelf()) + 90;
    }
    }
    }

    unload() {
        super.unload();
        $2Notifier.Notifier.changeListener(false, $2ListenID.ListenID.Tower_FightRoundTickStart, this.tickChange, this);
    }

    excute(t) {
        super.excute();
        b = this.launchPoint;
        var o = this.getBulletVo({
        lifeTime: 999,
        speed: this.cutVo.barrangeSpeed,
        startPos: b
    });
        o.shootDir = t.position.sub(b).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o);
    }

    fire(e) {
        $2Game.Game.mgr.LatticeElementMap.seekByPos({
        pos: e.pos,
        radius: this.dis,
        targetCamp: [this._owner.atkCamp],
        minRadius: 100
    }).slice(0, this.cutVo.barrageNum).forEach(function (o) {
        b = e.pos;
        var i = this.getBulletVo({
        lifeTime: 999,
        speed: this.cutVo.barrangeSpeed,
        startPos: b
    });
        i.shootDir = o.position.sub(b).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i);
    });
    }

}

@ccclass
export class Skill_MultiBullet extends $2SkillModule.BaseSkill {
    audioID: any;
    fire: any;
    radioScattering: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_dcj;
    }

    excute(t) {
        super.excute();
        this.fire({
        pos: t[0].position
    });
    }

    fire(e) {
        this.radioScattering(e.pos).forEach(function (e) {
        var o = this.getBulletVo({
        startPos: this.launchPoint
    });
        o.shootDir = e;
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o);
    });
    }

}

@ccclass
export class Skill_StruckLightning extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    dis: any;
    getBulletVo: any;

    excute() {
        var o = $2GameUtil.GameUtil.getRandomInArray($2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis
    }), this.cutVo.barrageNum);
        if (o.length) {
        super.excute();
        o.forEach(function (e, o) {
        var i = e.position.clone();
        this.owner.delayByGame(function () {
        var e = this.getBulletVo({
        startPos: i
    });
        $2Game.Game.mgr.spawnBullet(e.bulletPath, e);
    }, this.cutVo.barrageCd * o);
    });
    }
    }

    fire(e) {
        this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.RandomPos && e.pos.addSelf($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), $2Game.Game.random(10, this.cutVo.dis)));
        var t = this.getBulletVo({
        startPos: e.pos
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t);
    }

}

@ccclass
export class Skill_Meteorite extends $2SkillModule.BaseSkill {
    audioID: any;
    cutVo: any;
    getBulletVo: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_ys;
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        var i = e.position.clone();
        this.owner.delayByGame(function () {
        this.fire({
        pos: i
    });
    }, this.cutVo.barrageCd * t);
    });
    }

    fire(e) {
        var t = this.getBulletVo({
        startPos: e.pos
    });
        $2Game.Game.mgr.showEffectByType(t.bulletPath, e.pos, true, -1, {
        scale: this.cutVo.scale
    }).then(function (e) {
        e.set(t);
    });
    }

}

@ccclass
export class Skill_RingFireballs extends $2SkillModule.BaseSkill {
    _owner: any;
    bisectionAngle: any;
    cutVo: any;

    excute(t) {
        super.excute(t);
        var i = this.cutVo.barrangeSpeed * (1 + this._owner.buffMgr.getAttr($2GameatrCfg.GameatrDefine.rotate));
        this.bisectionAngle.forEach(function (e) {
        var t = this.getBulletVo({
        speed: i
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
        opacity: 0
    }).then(function (t) {
        t.node.setParent(this.showTier);
        t.offset = cc.v2(0, 50);
        t.cutAngle = e;
        $2Game.Game.tween(t.node).to(.1, {
        opacity: 255
    }).start();
        this.cutBullet.push(t);
    });
    });
    }

}

@ccclass
export class Skill_RangeBomb extends $2SkillModule.BaseSkill {
    _owner: any;
    audioID: any;
    fire: any;
    getBulletVo: any;
    showTier: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_bb;
    }

    excute() {
        super.excute();
        this.fire({
        pos: this._owner.position
    });
    }

    fire(e) {
        var t = this.getBulletVo({
        speed: 0,
        startPos: e.pos
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
        parent: this.showTier
    });
    }

}

@ccclass
export class Skill_LaserRadiation extends $2SkillModule.BaseSkill {
    excuteEnd: any;
    targetList: any;

    checkTarget() {
        var e = this.targetList;
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        var i = this.getBulletVo({});
        this.owner.delayByGame(function () {
        var t = this.launchPoint;
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i).then(function (i) {
        i.node.angle = $2GameUtil.GameUtil.GetAngle(t, e.position) + 90;
        i.atkTaget = e;
        i._maxWidth = 120;
        i.offset.set(this.mgr.launchPoint);
        i.set(this._owner);
    });
    }, this.cutVo.barrageCd * t);
    });
    }

}

@ccclass
export class Skill_LaserAnacampsis extends $2SkillModule.BaseSkill {
    _owner: any;
    dis: any;
    excuteEnd: any;

    checkTarget() {
        var e = $2Game.Game.mgr.LatticeElementMap.seek(this._owner, this.dis);
        e.sort(function (e, t) {
        return t.curHp - e.curHp;
    });
        if (e.length) {
        return this.excute(e.splice(0, 1));
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        var i = this.getBulletVo({
        lifeTime: this.cutVo.dur
    });
        this.owner.delayByGame(function () {
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i, {
        opacity: 0
    }).then(function (t) {
        t.set({
        startTarget: this._owner,
        endTarget: e,
        firstTarget: e,
        canAnsNum: this.cutVo.barrageNum - 1
    });
    });
    }, this.cutVo.barrageCd * t);
    });
    }

}

@ccclass
export class Skill_Ligature extends $2SkillModule.BaseSkill {
    cutVo: any;
    dis: any;
    game: any;
    getBulletVo: any;

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        this.owner.delayByGame(function () {
        this.fire({
        start: this._owner,
        end: e
    });
    }, this.cutVo.barrageCd * t);
    });
    }

    fire(e) {
        var t = this.getBulletVo({
        lifeTime: this.cutVo.dur
    });
        if (!e.end) {
        var o = this.game.LatticeElementMap.seekByPos({
        pos: e.start.position,
        targetCamp: t.atkCamp,
        radius: this.dis
    });
        e.end = $2GameUtil.GameUtil.randomArr(o);
    }
        if (e.end) {
        var i = $2GameUtil.GameUtil.GetAngle(e.start.position, e.end.position) + 90;
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
        opacity: 0,
        angle: i
    }).then(function (t) {
        t.setTarget(e.start, e.end);
    });
    }
    }

}

@ccclass
export class Skill_LaserRadiationGuard extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    dis: any;

    excute() {
        super.excute();
        b = this._owner.position;
        var o = .4 * cc.winSize.width;
        var i = .4 * cc.winSize.height;
        var n = [b.x - o, b.x + o];
        var r = [i - 200, i];
        $2GameUtil.GameUtil.getRandomInArray($2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis
    }), this.cutVo.barrageNum).forEach(function (e, o) {
        var i = cc.v2($2Game.Game.random(n[0], n[1]), b.y + $2Game.Game.random(r[0], r[1]) * $2GameUtil.GameUtil.getRandomInArray([1, -1], 1)[0]);
        var a = this.getBulletVo({
        lifeTime: this.cutVo.dur,
        speed: 0,
        startPos: i
    });
        this.owner.delayByGame(function () {
        $2Game.Game.mgr.spawnBullet(a.bulletPath, a).then(function (t) {
        this.node.angle = $2GameUtil.GameUtil.GetAngle(a.startPos, e.position) + 90;
        this.isMove = false;
        this._maxWidth = 50;
        this.set(null);
    });
    }, this.cutVo.barrageCd * o);
    });
    }

}

@ccclass
export class Skill_Arrows extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    dis: any;
    excuteEnd: any;

    checkTarget() {
        var e = $2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis,
        maxNum: this.cutVo.barrageNum
    });
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e) {
        var t = this.cutVo.dur / this.cutVo.barrageCd;
        var i = e.position;
        $2Game.Game.mgr.showSkillHerald("entity/fight/effect/Effect_Herald", i, 2 * this.cutVo.area + 50, this.cutVo.dur + 1);
        var n = function (e) {
        var t = i.add($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), $2Game.Game.random(0, this.cutVo.area)));
        this.owner.delayByGame(function () {
        $2Game.Game.mgr.showEffectByType("entity/fight/effect/Effect_" + this.skillMainID, t, true, 1).then(function (e) {
        e.set(this.getBulletVo({
        startPos: t
    }));
    });
    }, this.cutVo.barrageCd * e);
    };
        for (var r = 0; r < t; r++) {
        n(r);
    }
    });
    }

}

@ccclass
export class Skill_Venom extends $2SkillModule.BaseSkill {
    _owner: any;
    fire: any;
    getBulletVo: any;
    showTier: any;

    excute() {
        super.excute();
        this.fire({
        pos: this._owner.position
    });
    }

    fire(e) {
        var t = this.getBulletVo({
        speed: 0,
        startPos: e.pos
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
        parent: this.showTier
    });
    }

}

@ccclass
export class Skill_Icicle extends $2SkillModule.BaseSkill {
    excuteEnd: any;
    launchPoint: any;
    targetList: any;

    checkTarget() {
        var e = this.targetList;
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        var i = this.launchPoint.clone();
        t.forEach(function (e, t) {
        this.owner.delayByGame(function () {
        var t = this.getBulletVo({
        lifeTime: 2,
        speed: this.cutVo.barrangeSpeed,
        startPos: i
    });
        t.shootDir = e.position.sub(i).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t).then(function (e) {
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.skill_hbz);
        e.onCollisionCall = function () {};
    });
    }, this.cutVo.barrageCd * t);
    });
    }

}

@ccclass
export class Skill_BounceThrowingKnife extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    dis: any;
    excuteEnd: any;

    checkTarget() {
        var e = $2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis,
        maxNum: this.cutVo.barrageNum
    });
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        this.owner.delayByGame(function () {
        (b = this.launchPoint).y += this._owner.node.height / 2;
        var t = this.getBulletVo({
        lifeTime: 5,
        speed: this.cutVo.barrangeSpeed,
        startPos: b
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t).then(function (t) {
        t.setTarget(e, this._param[0]);
        cc.Tween.stopAllByTarget(t.node.children[0]);
    });
    }, this.cutVo.barrageCd * t);
    });
    }

}

@ccclass
export class Skill_Whirlwind extends $2SkillModule.BaseSkill {
    bisectionAngle: any;
    cutVo: any;

    excute() {
        super.excute();
        var o = this.cutVo.barrangeSpeed;
        this.bisectionAngle.forEach(function () {
        var e = this.getBulletVo({
        speed: o,
        lifeTime: this.cutVo.dur
    });
        $2Game.Game.mgr.spawnBullet(e.bulletPath, e).then(function (e) {
        e.offset = cc.v2(0, 50);
    });
    });
    }

}

@ccclass
export class Skill_Tornado extends $2SkillModule.BaseSkill {
    _owner: any;
    audioID: any;
    cutVo: any;
    dis: any;
    excuteEnd: any;
    getBulletVo: any;
    radioAngle: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_jf;
    }

    checkTarget() {
        var e = $2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis,
        maxNum: this.cutVo.barrageNum
    });
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        this.owner.delayByGame(function () {
        b = e.position.sub(this.launchPoint).normalizeSelf();
        var t = this.getBulletVo({
        shootDir: b
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t).then(function () {});
    }, this.cutVo.barrageCd * t);
    });
    }

    fire(e) {
        b.set($2GameUtil.GameUtil.AngleAndLenToPos($2GameUtil.GameUtil.randomArr(this.radioAngle)));
        var t = this.getBulletVo({
        startPos: e.pos,
        shootDir: b
    });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t);
    }

}

@ccclass
export class Skill_Continuous extends $2SkillModule.BaseSkill {
    cutVo: any;
    fire: any;
    getBulletVo: any;
    launchPoint: any;
    showTier: any;

    excute() {
        super.excute();
        this.fire({
        pos: this.launchPoint
    });
    }

    fire(e) {
        var o = this.getBulletVo({
        lifeTime: this.cutVo.dur,
        startPos: e.pos
    });
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o, {
        parent: this.showTier
    }).then(function (e) {
        [1440].includes(this.skillMainID) && e.node.getORaddComponent($2AutoFollow.default).offset.set(this.mgr.launchPoint);
    });
    }

}

@ccclass
export class Skill_GoldenCudgel extends $2SkillModule.BaseSkill {
    _owner: any;
    audioID: any;
    cutVo: any;
    getBulletVo: any;
    isStartSkill: any;
    setAm: any;

    constructor() {
        super();
        this.audioID = $2SoundCfg.SoundDefine.skill_bjsf;
    }

    excute() {
        super.excute();
        var o = this.getBulletVo({
        lifeTime: this.cutVo.dur,
        startPos: this._owner.position
    });
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o).then(function (e) {
        cc.Tween.stopAllByTarget(e.node);
        $2Game.Game.tween(e.node).by(.3, {
        angle: this.cutVo.barrangeSpeed
    }).repeatForever().start();
        e.offset.set(this.mgr.launchPoint);
    });
        this.isStartSkill && this.setAm();
    }

    setAm() {

        var o = null === (t = null === (e = this._owner) || e === undefined ? undefined : e.mySkeleton) || t === undefined ? undefined : t.setAnimation(1, this._owner.curStateTag == $2StateMachine.State.Type.IDLE ? "attack01" : "attack02", false);
        o.alpha = 1;
        cc.Tween.stopAllByTarget(o);
        $2Game.Game.tween(o).delay(this.cutVo.dur).to(.3, {
        alpha: 0
    }).start();
    }

}

@ccclass
export class Skill_SwordSword extends $2SkillModule.BaseSkill {
    fire: any;
    launchPoint: any;
    radioScattering: any;

    excute() {
        super.excute();
        this.fire();
    }

    fire() {
        var t = this.launchPoint;
        this.radioScattering().forEach(function (o, i) {
        this.owner.delayByGame(function () {
        b = t.add(o.mul(100));
        var i = this.getBulletVo({
        lifeTime: 1,
        speed: 700,
        startPos: b
    });
        i.shootDir = o.clone();
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i);
    }, this.cutVo.barrageCd * i);
    });
    }

}

@ccclass
export class Skill_EffectSkill extends $2SkillModule.BaseSkill {
    _owner: any;
    cutVo: any;
    excuteEnd: any;
    getBulletVo: any;
    isStartSkill: any;
    owner: any;
    setAm: any;
    showTier: any;
    targetList: any;

    checkTarget() {
        var e = this.targetList;
        if (e.length) {
        return this.excute(e.map(function (e) {
        return e.position;
    }));
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        var i = e.clone();
        this.owner.delayByGame(function () {
        this.fire({
        pos: i
    });
    }, this.cutVo.barrageCd * t);
    });
        this.isStartSkill && this.setAm();
    }

    setAm() {

        var o = null === (t = null === (e = this._owner) || e === undefined ? undefined : e.mySkeleton) || t === undefined ? undefined : t.setAnimation(1, "attack02", false);
        o.timeScale = 1 / this.cutVo.cd * 2;
        this.owner.delayByGame(function () {
        o.alpha = .5;
    }, o.animationEnd * o.timeScale * .5);
    }

    fire(e) {
        var t = this.getBulletVo({
        startPos: e.pos,
        size: this.cutVo.area * this.cutVo.scale
    });
        $2Game.Game.mgr.showEffectByType(t.bulletPath, e.pos, true, this.cutVo.dur, {
        parent: this.showTier
    }).then(function (e) {
        e.set(t);
    });
    }

}

@ccclass
export class Skill_ColdAir extends $2SkillModule.BaseSkill {
    game: any;

    excute() {
        super.excute();
        this.game.elementMap.forEach(function (e) {
        e.isValid && !e.isDead && (e.isInvincible || (e.campType, this._owner.atkCamp));
    });
    }

}

@ccclass
export class Skill_PosExcute extends $2SkillModule.BaseSkill {
    bulletNum: any;
    fireNum: any;
    game: any;
    getBulletVo: any;
    launchPoint: any;
    rangeRadio: any;

    constructor() {
        super();
        this.fireNum = 0;
    }

    load() {
        super.load();
        this.fireNum = 0;
    }

    excute() {
        super.excute();
        this.bulletNum > 100 || this.rangeRadio.forEach(function (e, o) {
        this.owner.delayByGame(function () {
        e.y += $2Game.Game.random(-10, 250);
        this.fire({
        pos: e
    });
    }, this.cutVo.barrageCd * o);
    });
    }

    fire(e) {
        var o = this.getBulletVo({
        startPos: this.launchPoint
    });
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o, {
        parent: this.game.botEffectNode,
        active: false
    }).then(function (o) {
        var i = cc.Vec2.distance(e.pos, this.launchPoint) / this.cutVo.barrangeSpeed;
        $2Game.Game.tween(o.node).set({
        active: true
    }).bezierTo(i, this.launchPoint, e.pos.add(cc.v2(0, 50)).multiply(cc.v2(.9, 1)), e.pos).call(function () {

        null === (e = o.collider) || e === undefined || e.setActive(true);
        126 == o.vo.bulletId && this.checkLigature(o);
    }).start();
    });
        this.fireNum++;
    }

    checkLigature(e) {
        C.length = 0;
        this.game.bulletList.forEach(function (t) {
        if (t.vo.bulletId == e.vo.bulletId) {
        if (!t.isActive) {
        return;
    }
        if (t == e) {
        return;
    }
        var o = cc.Vec2.squaredDistance(e.position, t.position);
        C.push({
        target: t,
        d: o
    });
    }
    });
        C.sort(function (e, t) {
        return e.d - t.d;
    });
        (C = C.splice(0, 3)).forEach(function (t) {
        e.connect.add(t.target);
        e.vo.belongSkill.checkSubSkill($2GameSeting.GameSeting.Release.SetActive, {
        start: e,
        end: t.target
    });
    });
    }

}

@ccclass
export class Skill_StampSweep extends $2SkillModule.BaseSkill {
    _owner: any;
    excuteEnd: any;
    getBulletVo: any;
    launchPoint: any;
    skillMainID: any;
    targetList: any;

    checkTarget() {
        var e = this.targetList;
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute(t) {
        super.excute();
        t.forEach(function (e, t) {
        var i = e.position.clone();
        this.owner.delayByGame(function () {
        this.fire({
        pos: i
    }, t);
    }, this.cutVo.barrageCd * t);
    });
    }

    fire(e, t) {
        var o;if ((null === (o = this._owner.buffMgr) || o === undefined ? undefined : o.isHasID([12600])) || [12620, 6200].includes(this.skillMainID)) {
        var n = this.getBulletVo({
        lifeTime: 2,
        startPos: this.launchPoint
    });
        var r = t % 2 == 0 ? 1 : -1;
        $2Game.Game.mgr.spawnBullet(n.bulletPath, n, {
        opacity: 1
    }).then(function (e) {
        $2Game.Game.tween(e.node).stopLast().set({
        opacity: 0,
        angle: 90 * r,
        scaleX: r * this.cutVo.scale
    }).to(.05, {
        opacity: 255
    }).call(function () {
        var e = "bones/skill/fx_bigsword";
        [12620].includes(this.skillMainID) && (e = "bones/skill/fx_sword5");
        $2Manager.Manager.loader.loadSpineNode(e, {
        nodeAttr: {
        parent: this.game._bulletNode,
        position: this.launchPoint,
        scaleX: r * this.cutVo.scale,
        scaleY: this.cutVo.scale
    },
        spAttr: {
        animation: "animation",
        type: $2GameSeting.GameSeting.TweenType.Game,
        loop: false,
        timeScale: 1.2 / this.game.gameSpeed
    },
        delayRemove: 1
    }, this.game.gameNode);
    }).parallel(cc.tween().to(.2, {
        opacity: 255
    }), cc.tween().by(.5, {
        angle: 180 * (t % 2 == 0 ? -1 : 1)
    }, {
        easing: cc.easing.sineOut
    })).call(function () {
        e.vo.lifeTime = .1;
    }).start();
    });
    } else {
        var s = $2GameUtil.GameUtil.GetAngle(this.launchPoint, e.pos) + 90;
        var c = this.launchPoint.add($2GameUtil.GameUtil.AngleAndLenToPos(s, 200)).clone();
        n = this.getBulletVo({
        lifeTime: 2,
        startPos: this.launchPoint
    });
        $2Game.Game.mgr.spawnBullet(n.bulletPath, n, {
        angle: s,
        opacity: 100,
        scale: .1
    }).then(function (e) {
        $2Game.Game.tween(e.node).set({
        scale: .1,
        angle: s
    }).parallel(cc.tween().to(.2, {
        scale: this.cutVo.scale
    }), cc.tween().to(.2, {
        position: c,
        opacity: 255
    }, {
        easing: cc.easing.sineOut
    })).delay(.1).to(.3, {
        position: this.launchPoint,
        opacity: 0
    }, {
        easing: cc.easing.sineOut
    }).call(function () {
        e.vo.lifeTime = 0;
    }).start();
    });
    }
    }

}

@ccclass
export class Skill_PowerStorage extends $2SkillModule.BaseSkill {
    _owner: any;
    excuteEnd: any;
    game: any;
    targetList: any;

    checkTarget() {
        var e = this.targetList;
        if (e.length) {
        return this.excute(e);
    }
        this.excuteEnd();
    }

    excute() {
        super.excute();
        this.game.showEffectByPath("bones/skill/fx_arch_addpower", {
        nodeAttr: {
        parent: this.game._bulletNode,
        position: this._owner.bodyPosition
    },
        spAttr: {
        loop: false,
        defaultAnim: "animation",
        isPlayerOnLoad: true
    }
    }).then(function (e) {
        e.setCompleteListener(function () {
        this.targetList.forEach(function (e, o) {
        var i = e.position.clone();
        this.owner.delayByGame(function () {
        b.set(this.launchPoint);
        var e = this.getBulletVo({
        startPos: b,
        targetPos: i
    });
        e.shootDir = i.sub(b).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(e.bulletPath, e);
    }, this.cutVo.barrageCd * o);
    });
        e.node.destroy();
    });
    });
    }

}

@ccclass
export class Skill_Flower extends $2SkillModule.BaseSkill {
    curAngle: any;
    cutVo: any;
    fire: any;
    owner: any;

    constructor() {
        super();
        this.curAngle = 0;
    }

    excute(t) {
        super.excute(t);
        this.fire({
        pos: this.owner.bodyPosition
    });
    }

    fire(e) {
        var o = function (o) {
        this.curAngle = (this.curAngle + this.cutVo.barrageAngle) % 360;
        var n = this.curAngle;
        this.owner.delayByGame(function () {
        var o = this.getBulletVo({
        startPos: e.pos,
        shootDir: $2GameUtil.GameUtil.AngleAndLenToPos(n)
    });
        this.game.spawnBullet(o.bulletPath, o);
    }, this.cutVo.barrageCd * o);
    };for (var n = 0; n < this.cutVo.barrageNum; n++) {
        o(n);
    }
    }

}

@ccclass
export class Skill_Magazine extends $2SkillModule.BaseSkill {
    cutMagazineNum: any;
    cutVo: any;
    game: any;
    getBulletVo: any;
    loadBulletMagazine: any;
    magazineReloadTime: any;
    magazineUI: any;
    totalMagazineNum: any;

    excute(t) {
        super.excute(t);
        t.forEach(function (e, t) {
        this.owner.delayByGame(function () {
        b.set(this.launchPoint);
        this.fire({
        pos: b,
        shootDir: this.owner.forwardDirection
    });
    }, this.cutVo.barrageCd * t + this.magazineReloadTime);
    });
    }

    fire(e) {
        var t = this.getBulletVo({
        startPos: e.pos,
        shootDir: e.shootDir
    });
        this.game.spawnBullet(t.bulletPath, t);
        this.cutMagazineNum--;
    }

    load() {
        super.load();
        this.loadBulletMagazine();
    }

    onBuff(t) {

        super.onBuff(t);
        null === (this.magazineUI) || this.magazineUI === undefined || this.magazineUI.resetState(this.cutMagazineNum, this.totalMagazineNum);
        this.cutVo.cd = Math.max(this.magazineReloadTime, this.cutVo.cd);
    }

}

@ccclass
export class Skill_FollowPath extends Skill_AtkDefault {
}

@ccclass
export class Skill_MCDragonFollowPath extends Skill_AtkDefault {
    game: any;
    getBulletVo: any;
    launchPoint: any;

    fire(e) {
        var t;if (this.game instanceof $2MChains.MChains.Mgr) {
        var i = this.game.chainsList && (null === (t = this.game.chainsList[0]) || t === undefined ? undefined : t.pathList);
        if (i) {
        var n = $2GameUtil.GameUtil.deepCopy(i).reverse();
        var r = this.getBulletVo({
        startPos: this.launchPoint
    });
        this.game.spawnBullet(r.bulletPath, r).then(function (t) {
        b.set(this.launchPoint).addSelf(e.pos).divSelf(2).addSelf(cc.v2(0, 200));
        var r = $2Intersection.Intersection.findClosestIndex(n, e.pos);
        var a = n.slice(r, i.length);
        var s = cc.Vec2.distance(this.launchPoint, e.pos) / 800;
        $2Game.Game.tween(t.node).bezierTo(s, this.launchPoint, b, e.pos).call(function () {
        t.set(a);
    }).by(.3, {
        y: -50
    }, {
        easing: cc.easing.bounceOut
    }).start();
    });
    }
    }
    }

}