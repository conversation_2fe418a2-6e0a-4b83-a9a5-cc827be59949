import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2GameatrCfg from "./GameatrCfg";
import $2GameSeting from "./GameSeting";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2Dragon from "./Dragon";
import $2Game from "./Game";
import $2PropertyVo from "./PropertyVo";
import $2ModeChainsModel from "./ModeChainsModel";
import $2MChains from "./MChains";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MCDragon extends $2Dragon.Dragon {
    // TODO: 添加属性和方法
}
