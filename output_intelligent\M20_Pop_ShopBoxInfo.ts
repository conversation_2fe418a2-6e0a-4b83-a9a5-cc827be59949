import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_Pop_ShopBoxInfo extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
