const fs = require('fs');
const path = require('path');

// 快速修正常见问题
function quickFixDirectory(dirPath) {
    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.ts'));
    
    console.log(`🔧 开始修正 ${files.length} 个文件...\n`);
    
    files.forEach(file => {
        const filePath = path.join(dirPath, file);
        quickFixFile(filePath);
    });
    
    console.log('✅ 修正完成!');
}

function quickFixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // 修正常见的类名错误
    const classNameFixes = {
        'BuffEcut': 'Buff_Excute',
        'BuffExcut': 'Buff_Excute',
        'BuffOnTime': 'Buff_OnTime',
        'BuffHPLink': 'Buff_HPLink',
        'BuffOnBehit': 'Buff_OnBehit',
        'BuffOnKill': 'Buff_OnKill',
        'BuffOnSpawnHurt': 'Buff_OnSpawnHurt'
    };
    
    Object.entries(classNameFixes).forEach(([wrong, correct]) => {
        const regex = new RegExp(`\\b${wrong}\\b`, 'g');
        if (content.includes(wrong)) {
            content = content.replace(regex, correct);
            hasChanges = true;
        }
    });
    
    // 修正其他常见问题
    const fixes = [
        // 修正变量声明残留
        { from: /^\s*var [etoi] = this;\s*$/gm, to: '' },
        
        // 修正多余的空行
        { from: /\n\s*\n\s*\n\s*\n/g, to: '\n\n' },
        
        // 修正方法参数中的多余空格
        { from: /\(\s*,/g, to: '(' },
        { from: /,\s*\)/g, to: ')' },
        
        // 修正 this 引用
        { from: /this\.this\./g, to: 'this.' }
    ];
    
    fixes.forEach(({ from, to }) => {
        const oldContent = content;
        content = content.replace(from, to);
        if (content !== oldContent) {
            hasChanges = true;
        }
    });
    
    if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 修正: ${path.basename(filePath)}`);
    }
}

// 使用示例
if (require.main === module) {
    const args = process.argv.slice(2);
    const targetDir = args[0] || './output_intelligent';
    
    console.log(`📁 目标目录: ${targetDir}\n`);
    quickFixDirectory(targetDir);
}

module.exports = { quickFixDirectory, quickFixFile };
