import $2GridViewCell from "./GridViewCell";
import $2VideoButton from "./VideoButton";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2MChains from "./MChains";
import $2TideDefendModel from "./TideDefendModel";
import $2MoreGamesView from "./MoreGamesView";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MoreGamesItem extends $2GridViewCell.default {
    // TODO: 添加属性和方法
}