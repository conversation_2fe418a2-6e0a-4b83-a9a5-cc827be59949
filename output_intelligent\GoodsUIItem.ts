import $2CallID from "./CallID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2UIManager from "./UIManager";
import $2StorageID from "./StorageID";
import $2GameUtil from "./GameUtil";
import $2EnergyStamp from "./EnergyStamp";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class GoodsUIItem extends cc.Component {
    // TODO: 添加属性和方法
}
