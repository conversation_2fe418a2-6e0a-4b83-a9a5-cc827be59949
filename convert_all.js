const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class AllInOneConverter {
    constructor() {
        this.inputDir = './scripts';
        this.outputDir = './output_all';
    }

    // 一键转换所有文件
    convertAll() {
        console.log('🚀 开始一键转换所有文件...\n');

        try {
            // 步骤 1: 批量转换
            console.log('📦 步骤 1: 批量转换 JS 到 TS...');
            this.runBatchConversion();

            // 步骤 2: 后处理优化
            console.log('\n🔧 步骤 2: 后处理优化...');
            this.runPostProcessing();

            // 步骤 3: 生成报告
            console.log('\n📊 步骤 3: 生成转换报告...');
            this.generateReport();

            console.log('\n🎉 所有文件转换完成!');
            console.log(`📁 输出目录: ${this.outputDir}`);
            console.log('💡 建议: 请检查转换结果并根据需要进行微调');

        } catch (error) {
            console.error('❌ 转换过程中出现错误:', error.message);
        }
    }

    // 运行批量转换
    runBatchConversion() {
        try {
            const result = execSync(`node final_enhanced_converter.js ${this.inputDir} ${this.outputDir}`, 
                { encoding: 'utf8', stdio: 'inherit' });
        } catch (error) {
            console.error('批量转换失败:', error.message);
            throw error;
        }
    }

    // 运行后处理
    runPostProcessing() {
        try {
            const result = execSync(`node post_process_all.js ${this.outputDir}`, 
                { encoding: 'utf8', stdio: 'inherit' });
        } catch (error) {
            console.error('后处理失败:', error.message);
            throw error;
        }
    }

    // 生成转换报告
    generateReport() {
        if (!fs.existsSync(this.outputDir)) {
            console.log('❌ 输出目录不存在');
            return;
        }

        const inputFiles = fs.readdirSync(this.inputDir).filter(f => f.endsWith('.js'));
        const outputFiles = fs.readdirSync(this.outputDir).filter(f => f.endsWith('.ts'));

        const report = {
            timestamp: new Date().toLocaleString(),
            inputDir: this.inputDir,
            outputDir: this.outputDir,
            totalInputFiles: inputFiles.length,
            totalOutputFiles: outputFiles.length,
            successRate: Math.round((outputFiles.length / inputFiles.length) * 100),
            successFiles: outputFiles.map(f => f.replace('.ts', '.js')),
            failedFiles: inputFiles.filter(f => !outputFiles.includes(f.replace('.js', '.ts')))
        };

        // 生成详细报告
        const reportContent = this.generateReportContent(report);
        const reportPath = path.join(this.outputDir, 'conversion_report.md');
        fs.writeFileSync(reportPath, reportContent, 'utf8');

        // 显示简要统计
        console.log(`📊 转换统计:`);
        console.log(`   总文件数: ${report.totalInputFiles}`);
        console.log(`   成功转换: ${report.totalOutputFiles}`);
        console.log(`   成功率: ${report.successRate}%`);
        
        if (report.failedFiles.length > 0) {
            console.log(`   失败文件: ${report.failedFiles.join(', ')}`);
        }

        console.log(`📄 详细报告: ${reportPath}`);
    }

    // 生成报告内容
    generateReportContent(report) {
        return `# JS to TS 转换报告

## 转换概览

- **转换时间**: ${report.timestamp}
- **输入目录**: ${report.inputDir}
- **输出目录**: ${report.outputDir}
- **总文件数**: ${report.totalInputFiles}
- **成功转换**: ${report.totalOutputFiles}
- **成功率**: ${report.successRate}%

## 转换详情

### ✅ 成功转换的文件 (${report.successFiles.length}个)

${report.successFiles.map(file => `- ${file}`).join('\n')}

${report.failedFiles.length > 0 ? `
### ❌ 转换失败的文件 (${report.failedFiles.length}个)

${report.failedFiles.map(file => `- ${file}`).join('\n')}

### 失败原因分析

转换失败的文件通常是以下类型：
1. **静态类/命名空间**: 不是标准的类定义结构
2. **特殊模块**: 导出函数而不是类
3. **复杂语法**: 使用了非标准的编译输出格式

### 处理建议

对于失败的文件，建议：
1. 手动检查文件结构
2. 参考成功转换的文件进行调整
3. 或保持原有的 JS 格式
` : ''}

## 转换质量

### 主要改进

1. **完整的类结构**: 包含属性声明、构造函数、方法
2. **正确的继承关系**: 自动识别并转换继承链
3. **智能属性提取**: 从方法体中分析使用的属性
4. **父类调用转换**: 自动转换为 TypeScript 语法
5. **代码清理**: 移除冗余变量，优化格式

### 需要注意的地方

1. **类型注解**: 所有属性默认为 \`any\` 类型，可根据需要细化
2. **方法体**: 保留了原始逻辑，可能需要进一步优化
3. **变量声明**: 部分临时变量可能需要手动清理

## 后续建议

1. **代码检查**: 建议使用 TypeScript 编译器检查语法
2. **类型完善**: 根据实际使用情况添加具体类型
3. **测试验证**: 确保转换后的代码功能正常
4. **逐步迁移**: 可以逐个文件进行详细调整

---
*报告生成时间: ${report.timestamp}*
`;
    }
}

// 使用示例
if (require.main === module) {
    const converter = new AllInOneConverter();
    
    const args = process.argv.slice(2);
    if (args.length >= 1) {
        converter.inputDir = args[0];
    }
    if (args.length >= 2) {
        converter.outputDir = args[1];
    }
    
    converter.convertAll();
}

module.exports = AllInOneConverter;
