import $2CallID from "./CallID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2AlertManager from "./AlertManager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2ADModel from "./ADModel";

const { ccclass, property, menu } = cc._decorator;

export default class ADController extends $2MVC.MVC.MController {
}
