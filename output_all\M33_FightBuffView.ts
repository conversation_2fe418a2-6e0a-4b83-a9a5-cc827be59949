import $2CallID from "./CallID";
import $2Listen<PERSON> from "./ListenID";
import $2VideoButton from "./VideoButton";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2ModeChainsModel from "./ModeChainsModel";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2BuffCardItem from "./BuffCardItem";
import $2MChains from "./MChains";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M33_FightBuffView extends $2Pop.Pop {
    // TODO: 添加属性和方法
}