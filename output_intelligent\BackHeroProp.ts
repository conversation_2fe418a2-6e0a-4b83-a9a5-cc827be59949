import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2Game from "./Game";
import $2Buff from "./Buff";
import $2BaseEntity from "./BaseEntity";
import $2OrganismBase from "./OrganismBase";
import $2SkillManager from "./SkillManager";
import $2PropertyVo from "./PropertyVo";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class BackHeroProp extends $2OrganismBase.default {
    // TODO: 添加属性和方法
}
