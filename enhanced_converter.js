const fs = require('fs');
const path = require('path');

class EnhancedJSToTSConverter {
    constructor() {
        this.importMap = new Map();
        this.propertySet = new Set();
    }

    // 增强的方法体转换
    enhanceMethodBody(body, className) {
        if (!body || !body.trim()) return '';
        
        let enhanced = body;
        
        // 1. 收集所有使用的属性
        const properties = this.extractUsedProperties(enhanced);
        
        // 2. 智能变量替换
        enhanced = this.smartVariableReplacement(enhanced);
        
        // 3. 清理和优化
        enhanced = this.cleanAndOptimize(enhanced);
        
        return { body: enhanced, properties };
    }

    // 提取使用的属性
    extractUsedProperties(body) {
        const properties = new Set();
        
        // 匹配 this.property 模式
        const thisProps = body.match(/this\.(\w+)/g);
        if (thisProps) {
            thisProps.forEach(prop => {
                const propName = prop.replace('this.', '');
                // 排除方法调用
                if (!body.includes(`${prop}(`)) {
                    properties.add(propName);
                }
            });
        }
        
        return Array.from(properties);
    }

    // 智能变量替换
    smartVariableReplacement(body) {
        let result = body;
        
        // 分析变量声明和使用
        const varDeclarations = this.analyzeVariableDeclarations(body);
        
        // 根据分析结果进行替换
        varDeclarations.forEach(({ varName, shouldReplace, replaceWith }) => {
            if (shouldReplace) {
                const regex = new RegExp(`\\b${varName}\\.`, 'g');
                result = result.replace(regex, `${replaceWith}.`);
                
                // 移除变量声明
                const declRegex = new RegExp(`var ${varName} = [^;]+;\\s*`, 'g');
                result = result.replace(declRegex, '');
            }
        });
        
        return result;
    }

    // 分析变量声明
    analyzeVariableDeclarations(body) {
        const declarations = [];
        
        // 匹配 var x = this; 模式
        const thisAssignments = body.match(/var (\w+) = this;/g);
        if (thisAssignments) {
            thisAssignments.forEach(assignment => {
                const varName = assignment.match(/var (\w+) = this;/)[1];
                declarations.push({
                    varName,
                    shouldReplace: true,
                    replaceWith: 'this'
                });
            });
        }
        
        // 匹配其他变量声明
        const otherVars = body.match(/var (\w+) = ([^;]+);/g);
        if (otherVars) {
            otherVars.forEach(assignment => {
                const match = assignment.match(/var (\w+) = ([^;]+);/);
                const varName = match[1];
                const value = match[2];
                
                // 如果变量只在局部使用，保留；如果是 this 的别名，替换
                if (value === 'this') {
                    declarations.push({
                        varName,
                        shouldReplace: true,
                        replaceWith: 'this'
                    });
                }
            });
        }
        
        return declarations;
    }

    // 清理和优化
    cleanAndOptimize(body) {
        return body
            // 修正父类调用
            .replace(/(\w+)\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$2($3)')
            .replace(/this\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)')
            
            // 转换常见JS语法
            .replace(/cc__assign/g, 'Object.assign')
            .replace(/null !== (\w+) && \w+ \|\| /g, '$1 || ')
            .replace(/undefined === (\w+)/g, '$1 === undefined')
            .replace(/null === (\w+)/g, '$1 === null')
            
            // 清理多余空行
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .trim();
    }

    // 增强的类代码生成
    generateEnhancedClassCode(classInfo) {
        const baseClass = classInfo.baseClass || '$2Buff.Buff.BuffItem';
        let code = `@ccclass\nexport class ${classInfo.className} extends ${baseClass} {\n`;
        
        // 收集所有属性
        const allProperties = new Set();
        
        // 从构造函数收集属性
        if (classInfo.constructor && classInfo.constructor.properties) {
            classInfo.constructor.properties.forEach(prop => {
                allProperties.add(prop.name);
            });
        }
        
        // 从方法收集属性
        if (classInfo.methods) {
            classInfo.methods.forEach(method => {
                if (method.properties) {
                    method.properties.forEach(prop => {
                        allProperties.add(prop);
                    });
                }
            });
        }
        
        // 声明属性
        if (allProperties.size > 0) {
            Array.from(allProperties).forEach(prop => {
                code += `    ${prop}: any;\n`;
            });
            code += '\n';
        }
        
        // 构造函数
        if (classInfo.constructor && classInfo.constructor.properties && classInfo.constructor.properties.length > 0) {
            code += '    constructor() {\n';
            code += '        super();\n';
            classInfo.constructor.properties.forEach(prop => {
                code += `        this.${prop.name} = ${prop.value};\n`;
            });
            code += '    }\n\n';
        }
        
        // 方法
        if (classInfo.methods) {
            classInfo.methods.forEach(method => {
                const params = method.params ? method.params.join(', ') : '';
                code += `    ${method.name}(${params}) {\n`;
                
                if (method.enhancedBody && method.enhancedBody.trim()) {
                    const lines = method.enhancedBody.split('\n');
                    lines.forEach(line => {
                        if (line.trim()) {
                            code += `        ${line.trim()}\n`;
                        }
                    });
                } else {
                    code += '        // TODO: 实现方法体\n';
                }
                
                code += '    }\n\n';
            });
        }
        
        code += '}\n';
        return code;
    }

    // 增强的多类转换
    convertMultiClassEnhanced(content) {
        const imports = this.extractImports(content);
        const classes = this.extractMultipleClassesEnhanced(content);
        
        if (!classes || classes.length === 0) {
            return null;
        }

        console.log(`📦 发现 ${classes.length} 个类，正在增强转换...`);

        let tsCode = '';
        
        if (imports.length > 0) {
            tsCode += imports.join('\n') + '\n\n';
        }
        
        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';
        
        classes.forEach((classInfo, index) => {
            tsCode += this.generateEnhancedClassCode(classInfo);
            if (index < classes.length - 1) {
                tsCode += '\n';
            }
        });

        return tsCode;
    }

    // 提取导入
    extractImports(content) {
        const imports = [];
        const regex = /var (\$\d*\$?\d*\w+) = require\("(\w+)"\);/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            const moduleName = match[1];
            const fileName = match[2];
            imports.push(`import ${moduleName} from "./${fileName}";`);
        }

        return imports;
    }

    // 增强的多类提取
    extractMultipleClassesEnhanced(content) {
        const classes = [];
        const lines = content.split('\n');
        let currentClass = null;
        let classContent = [];
        let braceCount = 0;
        let inClass = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            const classMatch = line.match(/var (exp_\w+) = function \(e\) \{/);
            if (classMatch) {
                if (currentClass) {
                    this.processClassEnhanced(currentClass, classContent, classes);
                }
                
                currentClass = {
                    fullVarName: classMatch[1],
                    className: classMatch[1].replace('exp_', ''),
                    startLine: i
                };
                classContent = [line];
                braceCount = 1;
                inClass = true;
                continue;
            }
            
            if (inClass) {
                classContent.push(line);
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;
                
                if (braceCount === 0 && line.includes('}(')) {
                    currentClass.baseClass = this.determineBaseClass(line);
                    this.processClassEnhanced(currentClass, classContent, classes);
                    currentClass = null;
                    classContent = [];
                    inClass = false;
                }
            }
        }
        
        return classes;
    }

    // 确定基类
    determineBaseClass(line) {
        let baseMatch = line.match(/\}(\(\$\d+\w+(?:\.\w+)*\));/);
        if (baseMatch) {
            return baseMatch[1].replace(/[()]/g, '');
        }
        
        baseMatch = line.match(/\}\((exp_\w+)\);/);
        if (baseMatch) {
            return baseMatch[1].replace('exp_', '');
        }
        
        return '$2Buff.Buff.BuffItem';
    }

    // 增强的类处理
    processClassEnhanced(classInfo, classContent, classes) {
        const fullContent = classContent.join('\n');
        const classDetails = this.extractClassDetailsEnhanced(fullContent);
        
        classes.push({
            ...classInfo,
            classContent: fullContent,
            ...classDetails
        });
    }

    // 增强的类详情提取
    extractClassDetailsEnhanced(classContent) {
        const details = {
            constructor: null,
            methods: [],
            decorators: []
        };

        if (classContent.includes('ccp_ccclass')) {
            details.decorators.push('@ccclass');
        }

        // 构造函数
        const constructorMatch = classContent.match(/function ([_\w]+)\(\) \{([\s\S]*?)\n  \}/);
        if (constructorMatch) {
            details.constructor = this.parseConstructorEnhanced(constructorMatch[2], constructorMatch[1]);
        }

        // 方法
        const methodPattern = /([_\w]+)\.prototype\.(\w+) = function \(([^)]*)\) \{([\s\S]*?)\n  \};/g;
        let methodMatch;
        
        while ((methodMatch = methodPattern.exec(classContent)) !== null) {
            const methodName = methodMatch[2];
            const methodParams = methodMatch[3] ? methodMatch[3].split(',').map(p => p.trim()).filter(p => p) : [];
            const methodBody = methodMatch[4];
            
            // 增强方法体处理
            const enhanced = this.enhanceMethodBody(methodBody, 'className');
            
            details.methods.push({
                name: methodName,
                params: methodParams,
                body: methodBody,
                enhancedBody: enhanced.body,
                properties: enhanced.properties
            });
        }

        return details;
    }

    // 增强的构造函数解析
    parseConstructorEnhanced(constructorBody, constructorVar) {
        const properties = [];
        const propPattern = /(\w+)\.(\w+) = ([^;]+);/g;
        let match;
        
        while ((match = propPattern.exec(constructorBody)) !== null) {
            if (match[1] === constructorVar || match[1] === 't' || match[1] === '_ctor') {
                properties.push({
                    name: match[2],
                    value: match[3]
                });
            }
        }
        
        return { properties };
    }

    // 转换文件
    convertFile(inputPath, outputPath) {
        try {
            const jsContent = fs.readFileSync(inputPath, 'utf8');
            
            // 检测是否为多类
            const isMultiClass = (jsContent.match(/var exp_\w+ = function \(e\) \{/g) || []).length > 1;
            
            let tsContent;
            if (isMultiClass) {
                tsContent = this.convertMultiClassEnhanced(jsContent);
            } else {
                // 单类处理逻辑
                tsContent = this.convertSingleClass(jsContent);
            }
            
            if (tsContent) {
                fs.writeFileSync(outputPath, tsContent, 'utf8');
                console.log(`✅ ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error(`❌ ${path.basename(inputPath)}: ${error.message}`);
            return false;
        }
    }

    // 简单的单类转换
    convertSingleClass(content) {
        // 这里可以实现单类转换逻辑
        return null;
    }
}

// 使用示例
if (require.main === module) {
    const converter = new EnhancedJSToTSConverter();
    
    const args = process.argv.slice(2);
    const inputFile = args[0] || './scripts/BuffList.js';
    const outputFile = args[1] || './output/BuffList_enhanced.ts';
    
    console.log(`📁 输入: ${inputFile}`);
    console.log(`📁 输出: ${outputFile}\n`);
    
    converter.convertFile(inputFile, outputFile);
}

module.exports = EnhancedJSToTSConverter;
