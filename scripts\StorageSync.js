Object.defineProperty(exports, "__esModule", {
  value: true
});
var def_StorageSync = function () {
  function _ctor() {}
  _ctor.getItem = function (e) {
    if (window.tt) {
      return window.wxapi.getStorageSync(e);
    }
  };
  _ctor.setItem = function (e, t) {
    if (window.tt) {
      return window.wxapi.setStorageSync(e, t);
    }
  };
  _ctor.removeItem = function (e) {
    if (window.tt) {
      return window.wxapi.removeStorageSync(e);
    }
  };
  return _ctor;
}();
exports.default = def_StorageSync;