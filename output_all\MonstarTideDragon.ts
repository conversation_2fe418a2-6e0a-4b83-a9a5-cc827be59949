import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2SoundCfg from "./SoundCfg";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2BaseEntity from "./BaseEntity";
import $2Dragon from "./Dragon";
import $2Game from "./Game";
import $2PropertyVo from "./PropertyVo";
import $2MChains from "./MChains";
import $2TideDefendModel from "./TideDefendModel";

const { ccclass, property, menu } = cc._decorator;

export default class MonstarTideDragon extends $2Dragon.Dragon {
    // TODO: 添加属性和方法
}