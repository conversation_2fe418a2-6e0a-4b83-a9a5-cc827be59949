import $2Cfg from "./Cfg";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2Buff from "./Buff";
import $2BaseEntity from "./BaseEntity";
import $2OrganismBase from "./OrganismBase";
import $2Game from "./Game";
import $2SkillManager from "./SkillManager";
import $2PropertyVo from "./PropertyVo";
import $2MTKnife from "./MTKnife";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MTKRole extends $2OrganismBase.default {
    // TODO: 添加属性和方法
}
