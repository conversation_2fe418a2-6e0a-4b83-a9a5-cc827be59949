import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2M20_PartItem from "./M20_PartItem";
import $2M20_ShopPartItem from "./M20_ShopPartItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_daily extends $2M20_ShopPartItem.default {
    // TODO: 添加属性和方法
}